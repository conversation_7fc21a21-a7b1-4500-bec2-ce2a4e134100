#!/usr/bin/env node
/**
 * Bot Repository Cleanup Script
 * 
 * This script will:
 * 1. Create a new clean branch for the bot repository
 * 2. Remove all web app files
 * 3. Move bot files from finmanager-bot/ to root
 * 4. Update package.json and other configs for bot-only setup
 * 5. Create proper bot repository structure
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

console.log('🧹 Starting Bot Repository Cleanup...\n');

// Files and directories that should be kept for the bot
const BOT_KEEP_FILES = [
  '.env.example',
  '.gitignore',
  'README.md',
  'package.json',
  'package-lock.json',
  'Dockerfile',
  'docker-compose.yml',
  // Bot-specific files from finmanager-bot/
  'index.js',
  'enterprise-bot.js',
  'test-bot.js',
  'start-bot.bat',
  'test-fast-bot.js',
  'FAST_BOT_DEPLOYMENT.md',
  // Keep some essential scripts
  'scripts/',
  'config/',
  'deployment/',
  'monitoring/',
  // Keep documentation that's bot-relevant
  'DATABASE_SETUP.md',
  'DOCKER_SETUP.md',
  'LOCAL_DEVELOPMENT.md',
  'SECURITY.md',
  'CONTRIBUTING.md',
  'DEPENDENCIES.md'
];

// Web app files/directories that should be removed
const WEB_APP_REMOVE_ITEMS = [
  'src/',
  'public/',
  'index.html',
  'vite.config.ts',
  'tailwind.config.js',
  'postcss.config.js',
  'tsconfig.json',
  'tsconfig.app.json',
  'tsconfig.node.json',
  'eslint.config.js',
  '.eslintrc.cjs',
  'jest.config.js',
  'jest.setup.js',
  'cypress/',
  'tests/',
  'coverage/',
  'pnpm-lock.yaml',
  '.tsbuildinfo',
  'vite.config.ts.timestamp-*',
  'FiNManageR.code-workspace',
  // Web app specific documentation
  'ACCESSIBILITY.md',
  'ARCHITECTURE.md',
  'MOBILE_OPTIMIZATION.md',
  'PERFORMANCE.md',
  'INTERNATIONALIZATION.md',
  'OFFLINE_FUNCTIONALITY.md',
  'USER_ONBOARDING.md',
  // Web app specific files that were mistakenly added
  'api/',
  'netlify/',
  'database/gcs-schema.sql',
  'google-service-account.json',
  // Cleanup files that are web-app specific
  'ATTACHMENT_SYSTEM_*.md',
  'AUTHENTICATION_FIXES_*.md',
  'BLOB_URL_FIX_*.md',
  'BUFFER_ERROR_FIX_*.md',
  'GCS_DEPLOYMENT_GUIDE.md',
  'GOOGLE_OAUTH_SETUP_GUIDE.md',
  'SYNCHRONIZATION_*.md',
  'SYNTAX_ERROR_FIX.md',
  // Performance fix HTML files
  '*.html',
  // Test data files
  'eng.traineddata'
];

async function cleanupBotRepository() {
  try {
    console.log('📋 Step 1: Creating backup of current bot files...');
    
    // Create backup directory
    const backupDir = `bot-backup-${new Date().toISOString().replace(/[:.]/g, '-')}`;
    await fs.mkdir(backupDir, { recursive: true });
    
    // Copy bot files to backup
    const botDir = 'finmanager-bot';
    if (await fileExists(botDir)) {
      await copyDirectory(botDir, path.join(backupDir, 'bot-files'));
      console.log(`✅ Bot files backed up to ${backupDir}/bot-files`);
    }
    
    console.log('\n🗑️ Step 2: Removing web app files...');
    
    // Remove web app files and directories
    for (const item of WEB_APP_REMOVE_ITEMS) {
      if (item.includes('*')) {
        // Handle glob patterns
        const files = await getGlobFiles(item);
        for (const file of files) {
          await removeItem(file);
        }
      } else {
        await removeItem(item);
      }
    }
    
    console.log('\n📁 Step 3: Moving bot files to root...');
    
    // Move bot files from finmanager-bot/ to root
    if (await fileExists(botDir)) {
      const botFiles = await fs.readdir(botDir);
      for (const file of botFiles) {
        const srcPath = path.join(botDir, file);
        const destPath = file;
        
        if (await fileExists(destPath)) {
          await fs.unlink(destPath);
        }
        
        await fs.rename(srcPath, destPath);
        console.log(`✅ Moved ${srcPath} → ${destPath}`);
      }
      
      // Remove empty finmanager-bot directory
      await fs.rmdir(botDir);
      console.log('✅ Removed empty finmanager-bot directory');
    }
    
    console.log('\n📝 Step 4: Updating bot configuration files...');
    
    // Update package.json for bot-only setup
    await updatePackageJson();
    
    // Create/update bot-specific README
    await createBotReadme();
    
    // Update .env.example for bot
    await updateEnvExample();
    
    console.log('\n🎯 Step 5: Creating bot repository structure...');
    
    // Create bot-specific directories if they don't exist
    const botDirs = ['logs', 'temp', 'uploads'];
    for (const dir of botDirs) {
      await fs.mkdir(dir, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    }
    
    // Create .gitignore for bot
    await createBotGitignore();
    
    console.log('\n✅ Bot repository cleanup completed successfully!');
    console.log('\n📊 Summary:');
    console.log('• ✅ Web app files removed');
    console.log('• ✅ Bot files moved to root');
    console.log('• ✅ Configuration updated');
    console.log('• ✅ Repository structure optimized');
    console.log(`• ✅ Backup created: ${backupDir}`);
    
    console.log('\n🚀 Next steps:');
    console.log('1. Review the changes');
    console.log('2. Test bot functionality: npm run simple');
    console.log('3. Commit and push: git add . && git commit -m "Clean up bot repository"');
    console.log('4. Deploy to your hosting platform');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    process.exit(1);
  }
}

// Helper functions
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function removeItem(itemPath) {
  try {
    if (await fileExists(itemPath)) {
      const stats = await fs.stat(itemPath);
      if (stats.isDirectory()) {
        await fs.rmdir(itemPath, { recursive: true });
        console.log(`🗑️ Removed directory: ${itemPath}`);
      } else {
        await fs.unlink(itemPath);
        console.log(`🗑️ Removed file: ${itemPath}`);
      }
    }
  } catch (error) {
    console.log(`⚠️ Could not remove ${itemPath}: ${error.message}`);
  }
}

async function copyDirectory(src, dest) {
  await fs.mkdir(dest, { recursive: true });
  const entries = await fs.readdir(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.copyFile(srcPath, destPath);
    }
  }
}

async function getGlobFiles(pattern) {
  // Simple glob implementation for cleanup
  const basePattern = pattern.replace('*', '');
  try {
    const files = await fs.readdir('.');
    return files.filter(file => file.startsWith(basePattern));
  } catch {
    return [];
  }
}

async function updatePackageJson() {
  try {
    const packagePath = 'package.json';
    if (await fileExists(packagePath)) {
      const packageData = JSON.parse(await fs.readFile(packagePath, 'utf8'));
      
      // Update for bot-only setup
      packageData.name = 'finmanager-telegram-bot';
      packageData.description = 'FiNManageR Telegram Bot - Enterprise Financial Assistant';
      packageData.main = 'enterprise-bot.js';
      packageData.type = 'commonjs';
      
      // Keep only bot-relevant scripts
      packageData.scripts = {
        start: 'node enterprise-bot.js',
        dev: 'nodemon enterprise-bot.js',
        simple: 'node index.js',
        test: 'node test-bot.js',
        health: 'curl -f http://localhost:3001/health || exit 1'
      };
      
      // Remove web app dependencies, keep only bot dependencies
      const botDependencies = {
        'node-telegram-bot-api': '^0.61.0',
        '@supabase/supabase-js': '^2.39.0',
        'dotenv': '^16.0.3',
        'express': '^4.18.2',
        'helmet': '^6.1.5',
        'cors': '^2.8.5',
        'compression': '^1.7.4',
        'winston': '^3.8.2',
        'multer': '^1.4.5-lts.1',
        'axios': '^1.4.0'
      };
      
      packageData.dependencies = botDependencies;
      packageData.devDependencies = {
        'nodemon': '^2.0.22'
      };
      
      await fs.writeFile(packagePath, JSON.stringify(packageData, null, 2));
      console.log('✅ Updated package.json for bot setup');
    }
  } catch (error) {
    console.log(`⚠️ Could not update package.json: ${error.message}`);
  }
}

async function createBotReadme() {
  const readmeContent = `# FiNManageR Telegram Bot

Enterprise Financial Assistant Telegram Bot for FiNManageR.

## 🚀 Quick Start

### Installation
\`\`\`bash
npm install
\`\`\`

### Configuration
1. Copy \`.env.example\` to \`.env\`
2. Fill in your bot token and Supabase credentials
3. Test configuration: \`npm test\`

### Running the Bot
\`\`\`bash
# Simple bot (recommended for testing)
npm run simple

# Full enterprise bot
npm start

# Development with auto-reload
npm run dev
\`\`\`

## 🤖 Bot Features

- ✅ Real-time transaction logging
- ✅ Natural language processing
- ✅ Voice message support
- ✅ Receipt scanning (OCR)
- ✅ Smart categorization
- ✅ Balance tracking
- ✅ Spending insights
- ✅ Multi-language support
- ✅ Enterprise security

## 📱 Commands

- \`/start\` - Welcome message
- \`/help\` - Command list
- \`/link <code>\` - Link FiNManageR account
- \`/expense <amount> <category> <description>\` - Log expense
- \`/income <amount> <category> <description>\` - Log income
- \`/balance\` - Check balance
- \`/recent\` - Recent transactions
- \`/insights\` - AI spending analysis

## 🔧 Configuration

Required environment variables:
- \`TELEGRAM_BOT_TOKEN\` - Your bot token from @BotFather
- \`SUPABASE_URL\` - Supabase project URL
- \`SUPABASE_SERVICE_ROLE_KEY\` - Supabase service role key

## 🚀 Deployment

See deployment guides in the \`deployment/\` directory.

## 📊 Monitoring

Health check endpoint: \`http://localhost:3001/health\`

## 🔒 Security

- Enterprise-grade security features
- Rate limiting
- Input validation
- Secure data handling

## 📝 License

Private - FiNManageR Project
`;

  await fs.writeFile('README.md', readmeContent);
  console.log('✅ Created bot-specific README.md');
}

async function updateEnvExample() {
  const envContent = `# FiNManageR Telegram Bot Configuration

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
BOT_USERNAME=Myfnmbot

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Server Configuration
PORT=3000
HEALTH_PORT=3001
NODE_ENV=production

# Optional: Logging
LOG_LEVEL=info
`;

  await fs.writeFile('.env.example', envContent);
  console.log('✅ Updated .env.example for bot');
}

async function createBotGitignore() {
  const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment
.env
.env.local
.env.production

# Logs
logs/
*.log

# Runtime
.pid
.seed
*.pid.lock

# Temporary files
temp/
uploads/
*.tmp

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Bot specific
bot-backup-*/
`;

  await fs.writeFile('.gitignore', gitignoreContent);
  console.log('✅ Updated .gitignore for bot');
}

// Run the cleanup
if (require.main === module) {
  cleanupBotRepository();
}

module.exports = { cleanupBotRepository };
