#!/usr/bin/env node
require('dotenv').config();

// Test Enhanced Services
console.log('🧪 Testing Enhanced Services...');

try {
  // Test I18n Service
  console.log('1️⃣ Testing I18n Service...');
  const I18nService = require('../scripts/i18n-service.cjs');
  const i18nService = new I18nService();
  console.log('✅ I18n Service loaded successfully');

  // Test Enhanced Transaction Service
  console.log('2️⃣ Testing Enhanced Transaction Service...');
  const EnhancedTransactionService = require('../scripts/enhanced-transaction-service.cjs');
  const { createClient } = require('@supabase/supabase-js');
  
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
  
  const enhancedTransactionService = new EnhancedTransactionService(supabase, i18nService);
  console.log('✅ Enhanced Transaction Service loaded successfully');

  // Test Enhanced Confirmation Service
  console.log('3️⃣ Testing Enhanced Confirmation Service...');
  const EnhancedConfirmationService = require('../scripts/enhanced-confirmation-service.cjs');
  
  // Create a mock bot for testing
  const mockBot = {
    sendMessage: () => Promise.resolve(),
    editMessageText: () => Promise.resolve(),
    answerCallbackQuery: () => Promise.resolve()
  };
  
  const enhancedConfirmationService = new EnhancedConfirmationService(mockBot, supabase, i18nService);
  console.log('✅ Enhanced Confirmation Service loaded successfully');

  console.log('🎉 All Enhanced Services are working correctly!');
  console.log('');
  console.log('📋 Service Status:');
  console.log('   ✅ I18n Service: Ready');
  console.log('   ✅ Enhanced Transaction Service: Ready');
  console.log('   ✅ Enhanced Confirmation Service: Ready');
  console.log('   ✅ Supabase Connection: Ready');
  console.log('');
  console.log('🚀 Your bot should now use the enhanced multi-step forms!');

} catch (error) {
  console.error('❌ Enhanced Services Test Failed:');
  console.error('Error:', error.message);
  console.error('Stack:', error.stack);
  console.log('');
  console.log('🔧 Possible Solutions:');
  console.log('1. Check if all script files exist in the scripts/ directory');
  console.log('2. Verify your .env file has correct Supabase credentials');
  console.log('3. Make sure all npm dependencies are installed');
  console.log('');
  console.log('Run: npm install');
  console.log('Then try: node test-enhanced-services.js');
}
