import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AmountInput } from './AmountInput';
import { CategorySelector } from './CategorySelector';
import { DateTimeSelector } from './DateTimeSelector';
import { SmartCategorySuggestion } from './SmartCategorySuggestion';
import { ChevronRight, Plus } from 'lucide-react';
import { usePaymentMethod } from '../../contexts/PaymentMethodContext';

interface BasicInfoStepProps {
  formData: any;
  updateFormData: (updates: any) => void;
  type: 'income' | 'expense';
  onNext: () => void;
  onCancel: () => void;
}

export function BasicInfoStep({ formData, updateFormData, type, onNext, onCancel }: BasicInfoStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { paymentMethods, loading: loadingPaymentMethods } = usePaymentMethod();
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const navigate = useNavigate();

  const validateStep = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }

    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }

    if (!formData.source) {
      newErrors.source = type === 'income' ? 'Please enter income source' : 'Please enter merchant/vendor';
    }

    if (type === 'expense' && !formData.paymentMethod) {
      newErrors.paymentMethod = 'Please select payment method';
    }

    if (!formData.date) {
      newErrors.date = 'Please select a date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      onNext();
    }
  };

  return (
    <div className="space-y-4">
      <AmountInput
        value={formData.amount}
        onChange={(value) => updateFormData({ amount: value })}
        error={errors.amount}
      />

      <CategorySelector
        type={type}
        selectedCategory={formData.category}
        selectedSubcategory={formData.subcategory}
        onCategoryChange={(category, subcategory) =>
          updateFormData({ category, subcategory })}
        error={errors.category}
      />

      <div>
        <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
          {type === 'income' ? 'Source' : 'Merchant/Vendor'}
        </label>
        <input
          id="source"
          name="source"
          type="text"
          value={formData.source}
          onChange={(e) => updateFormData({ source: e.target.value })}
          className={`w-full rounded border px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.source
              ? 'border-red-300 text-red-900 placeholder-red-300'
              : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'
          }`}
          placeholder={type === 'income' ? 'e.g., Employer, Client' : 'e.g., Amazon, Grocery Store'}
        />
        {errors.source && (
          <p className="mt-1 text-sm text-red-600">{errors.source}</p>
        )}

        {/* Smart category suggestion based on source/description */}
        <SmartCategorySuggestion
          description={formData.source}
          type={type}
          currentCategory={formData.category}
          onSelectSuggestion={(category) => updateFormData({ category })}
        />
      </div>

      {type === 'expense' && (
        <div>
          <div className="flex justify-between items-center mb-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Payment Method
            </label>
            <button
              type="button"
              onClick={() => setShowAddPaymentMethod(!showAddPaymentMethod)}
              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center"
            >
              <Plus className="h-3 w-3 mr-1" />
              Manage
            </button>
          </div>

          {/* Payment Method Dropdown */}
          <select
            value={formData.paymentMethod}
            onChange={(e) => {
              const selectedMethod = paymentMethods.find(method => method.name === e.target.value);
              updateFormData({
                paymentMethod: e.target.value,
                // Store the credit card ID if this is a credit card payment method
                creditCardId: selectedMethod?.type === 'creditCard' ? selectedMethod.creditCardId : undefined
              });
            }}
            className={`w-full rounded border px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.paymentMethod
                ? 'border-red-300 text-red-900'
                : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'
            }`}
          >
            <option value="">Select payment method</option>
            {paymentMethods.map(method => (
              <option key={method.id} value={method.name}>
                {method.type === 'creditCard'
                  ? `Card ${method.name.includes('XXXX') ? method.name : `${method.name} (${method.creditCardId?.substring(0, 4)})`}`
                  : (method.icon ? `${method.name} ${method.icon}` : method.name)
                }
                {method.isDefault ? ' (Default)' : ''}
                {method.type === 'creditCard' && method.availableCredit !== undefined
                  ? ` - Available: ${new Intl.NumberFormat('en-IN', {
                      style: 'currency',
                      currency: 'INR',
                      maximumFractionDigits: 0
                    }).format(method.availableCredit)}`
                  : ''
                }
              </option>
            ))}
          </select>

          {errors.paymentMethod && (
            <p className="mt-1 text-sm text-red-600">{errors.paymentMethod}</p>
          )}

          {/* Add Payment Method UI (conditionally shown) */}
          {showAddPaymentMethod && (
            <div className="mt-2 p-3 border border-blue-200 dark:border-blue-800 rounded-md bg-blue-50 dark:bg-blue-900/20">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                Manage Payment Methods
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                You can manage your payment methods in the Settings page.
              </p>
              <button
                type="button"
                onClick={() => {
                  // Navigate to settings page with financial tab active
                  navigate('/settings?tab=financial');
                  setShowAddPaymentMethod(false);
                }}
                className="w-full text-center text-xs bg-blue-600 text-white py-1 px-2 rounded hover:bg-blue-700"
              >
                Go to Settings
              </button>
            </div>
          )}
        </div>
      )}

      <DateTimeSelector
        date={formData.date}
        time={formData.time}
        onDateChange={(date) => updateFormData({ date })}
        onTimeChange={(time) => updateFormData({ time })}
        error={errors.date}
      />

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleNext}
          className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
        >
          Next
          <ChevronRight className="ml-1 h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
