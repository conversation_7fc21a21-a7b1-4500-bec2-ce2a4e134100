import React, { useState } from 'react';
import { useCategories, Category } from '../../contexts/CategoryContext';
import { useToast } from '../../components/ui/use-toast';

interface CategorySelectorProps {
  type: 'income' | 'expense';
  selectedCategory: string;
  selectedSubcategory?: string;
  onCategoryChange: (category: string, subcategory?: string) => void;
  error?: string;
}

// Define subcategories for main categories
// This could be moved to CategoryContext in the future
const SUBCATEGORIES = {
  income: {
    'Salary': ['Regular', 'Bonus', 'Commission', 'Overtime'],
    'Freelance': ['Consulting', 'Writing', 'Design', 'Development', 'Other'],
    'Business': ['Sales', 'Services', 'Investments', 'Other'],
    'Investment': ['Dividends', 'Interest', 'Capital Gains', 'Rental', 'Other'],
    'Gifts': ['Family', 'Friends', 'Other'],
    'Refunds': ['Product Returns', 'Tax Refunds', 'Reimbursements', 'Other'],
  },
  expense: {
    'Food & Dining': ['Groceries', 'Restaurants', 'Fast Food', 'Coffee Shops', 'Food Delivery'],
    'Transportation': ['Fuel', 'Public Transit', 'Taxi/Rideshare', 'Parking', 'Tolls', 'Maintenance'],
    'Shopping': ['Clothing', 'Electronics', 'Home Goods', 'Personal Care', 'Gifts'],
    'Entertainment': ['Movies', 'Music', 'Games', 'Streaming Services', 'Events', 'Hobbies'],
    'Bills & Utilities': ['Electricity', 'Water', 'Gas', 'Internet', 'Mobile', 'Cable/DTH', 'Subscriptions'],
    'Housing': ['Rent', 'Mortgage', 'Maintenance', 'Furniture', 'Appliances', 'Domestic Help'],
    'Healthcare': ['Doctor', 'Dentist', 'Medicines', 'Insurance', 'Fitness'],
    'Education': ['Tuition', 'Books', 'Courses', 'Supplies'],
    'Travel': ['Flights', 'Hotels', 'Transportation', 'Food', 'Activities'],
    'Personal': ['Grooming', 'Clothing', 'Accessories'],
    'Taxes': ['Income Tax', 'Property Tax', 'GST/Sales Tax'],
    'Insurance': ['Health', 'Life', 'Vehicle', 'Home', 'Travel'],
    'Investments': ['Stocks', 'Mutual Funds', 'Fixed Deposits', 'Crypto', 'Other'],
    'Debt': ['Credit Card', 'Loan Payment', 'EMI'],
    'Gifts & Donations': ['Charity', 'Gifts', 'Religious'],
  }
};

// Category icons (you can replace these with actual icon components)
const CATEGORY_ICONS = {
  'Salary': '💼',
  'Freelance': '💻',
  'Business': '🏢',
  'Investment': '📈',
  'Gifts': '🎁',
  'Refunds': '↩️',
  'Other Income': '💰',
  'Food & Dining': '🍔',
  'Transportation': '🚗',
  'Shopping': '🛍️',
  'Entertainment': '🎬',
  'Bills & Utilities': '📱',
  'Housing': '🏠',
  'Healthcare': '🏥',
  'Education': '🎓',
  'Travel': '✈️',
  'Personal': '👤',
  'Taxes': '📝',
  'Insurance': '🔒',
  'Investments': '💹',
  'Debt': '💳',
  'Gifts & Donations': '🎀',
  'Other': '📦',
  'Miscellaneous': '📋'
};

export function CategorySelector({
  type,
  selectedCategory,
  selectedSubcategory,
  onCategoryChange,
  error
}: CategorySelectorProps) {
  const { expenseCategories, incomeCategories, addCategory, checkCategoryExists, loading: categoriesLoading } = useCategories();
  const { toast } = useToast();
  const [showCustomCategory, setShowCustomCategory] = useState(false);
  const [customCategory, setCustomCategory] = useState('');

  // Add a new custom category with duplicate checking
  const addCustomCategory = async () => {
    const trimmedName = customCategory.trim();
    if (!trimmedName) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    // Check if category already exists using the helper function
    if (checkCategoryExists(trimmedName, type)) {
      toast({
        title: 'Duplicate Category',
        description: `A category named "${trimmedName}" already exists for ${type} transactions`,
        variant: 'destructive',
      });
      return;
    }

    try {
      // Add the category to the database via CategoryContext
      await addCategory({
        name: trimmedName,
        type: type,
        isDefault: false,
      });

      // Select the new category
      onCategoryChange(trimmedName);
      setCustomCategory('');
      setShowCustomCategory(false);

      toast({
        title: 'Success',
        description: `Category "${trimmedName}" added successfully`,
      });
    } catch (error: any) {
      // Error handling is now done in CategoryContext
      // Just log the error here for debugging
      console.error('Category creation failed:', error);
    }
  };

  // Get all categories based on type
  const categories = type === 'expense' ? expenseCategories : incomeCategories;

  // Get subcategories for the selected main category if available
  const subcategories = selectedCategory &&
    SUBCATEGORIES[type] &&
    SUBCATEGORIES[type][selectedCategory as keyof typeof SUBCATEGORIES[typeof type]] || [];

  return (
    <div className="space-y-2">
      <div>
        <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Category
        </label>
        <div className="mt-1 relative">
          {showCustomCategory ? (
            <div className="flex space-x-2">
              <input
                id="custom-category"
                name="customCategory"
                type="text"
                value={customCategory}
                onChange={(e) => setCustomCategory(e.target.value)}
                className="block w-full px-3 py-2 sm:text-sm rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter custom category"
                disabled={categoriesLoading}
                aria-label="Custom category name"
              />
              <button
                type="button"
                onClick={addCustomCategory}
                className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                disabled={categoriesLoading || !customCategory.trim()}
              >
                Add
              </button>
              <button
                type="button"
                onClick={() => setShowCustomCategory(false)}
                className="px-3 py-2 bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
                disabled={categoriesLoading}
              >
                Cancel
              </button>
            </div>
          ) : (
            <>
              <select
                id="category"
                name="category"
                value={selectedCategory}
                onChange={(e) => {
                  const newCategory = e.target.value;
                  if (newCategory === '__custom__') {
                    setShowCustomCategory(true);
                  } else {
                    onCategoryChange(newCategory);
                  }
                }}
                className={`block w-full px-3 py-2 sm:text-sm rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  error ? 'border-red-300 text-red-900' : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'
                }`}
                disabled={categoriesLoading}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {CATEGORY_ICONS[category.name as keyof typeof CATEGORY_ICONS] || '📋'} {category.name}
                  </option>
                ))}
                <option value="__custom__">+ Add Custom Category</option>
              </select>
            </>
          )}
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600" id="category-error">
            {error}
          </p>
        )}
      </div>

      {/* Subcategory selector (only show if the selected category has subcategories) */}
      {selectedCategory && subcategories.length > 0 && (
        <div>
          <label htmlFor="subcategory" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Subcategory
          </label>
          <select
            id="subcategory"
            value={selectedSubcategory || ''}
            onChange={(e) => onCategoryChange(selectedCategory, e.target.value)}
            className="mt-1 block w-full px-3 py-2 sm:text-sm rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
            disabled={categoriesLoading}
          >
            <option value="">Select a subcategory (optional)</option>
            {subcategories.map((subcategory) => (
              <option key={subcategory} value={subcategory}>
                {subcategory}
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
}
