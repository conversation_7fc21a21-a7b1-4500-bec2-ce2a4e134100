#!/usr/bin/env node

/**
 * Debug script to test database connection and transaction saving
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 FiNManageR Database Debug Tool');
console.log('=====================================');

async function debugDatabase() {
  try {
    // Test 1: Environment Variables
    console.log('\n📋 Test 1: Environment Variables');
    console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing');

    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.log('❌ Missing required environment variables');
      return;
    }

    // Test 2: Supabase Connection
    console.log('\n📋 Test 2: Supabase Connection');
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    console.log('✅ Supabase client created');

    // Test 3: Check Tables Exist
    console.log('\n📋 Test 3: Check Required Tables');
    
    const tables = ['telegram_users', 'transactions', 'budgets'];
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: Accessible`);
        }
      } catch (err) {
        console.log(`❌ ${table}: ${err.message}`);
      }
    }

    // Test 4: Check Telegram Users
    console.log('\n📋 Test 4: Check Telegram Users');
    const { data: users, error: usersError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('is_active', true);

    if (usersError) {
      console.log('❌ Error fetching telegram users:', usersError.message);
    } else {
      console.log(`✅ Found ${users?.length || 0} active telegram users`);
      if (users && users.length > 0) {
        console.log('📋 Sample user:', {
          id: users[0].id,
          telegram_id: users[0].telegram_id,
          user_id: users[0].user_id,
          linked_at: users[0].linked_at
        });
      }
    }

    // Test 5: Test Transaction Insert (with rollback)
    console.log('\n📋 Test 5: Test Transaction Insert');
    
    if (users && users.length > 0) {
      const testUser = users[0];
      
      try {
        const { data: testTransaction, error: insertError } = await supabase
          .from('transactions')
          .insert({
            user_id: testUser.user_id,
            amount: 1,
            type: 'expense',
            category: 'test',
            description: 'debug test transaction',
            date: new Date().toISOString(),
            source: 'debug_test', // Required field!
            source_type: 'debug_test'
          })
          .select()
          .single();

        if (insertError) {
          console.log('❌ Transaction insert failed:', insertError.message);
          console.log('❌ Error details:', insertError);
        } else {
          console.log('✅ Transaction insert successful:', testTransaction.id);
          
          // Clean up test transaction
          await supabase
            .from('transactions')
            .delete()
            .eq('id', testTransaction.id);
          console.log('✅ Test transaction cleaned up');
        }
      } catch (err) {
        console.log('❌ Transaction test error:', err.message);
      }
    } else {
      console.log('⚠️ No telegram users found - cannot test transaction insert');
    }

    // Test 6: Check Database Functions
    console.log('\n📋 Test 6: Check Database Functions');
    
    if (users && users.length > 0) {
      try {
        const { data: insights, error: insightsError } = await supabase
          .rpc('get_user_insights_summary', { p_user_id: users[0].user_id });

        if (insightsError) {
          console.log('❌ Insights function error:', insightsError.message);
        } else {
          console.log('✅ Insights function working');
        }
      } catch (err) {
        console.log('❌ Insights function test error:', err.message);
      }
    }

    console.log('\n🎉 Database Debug Complete!');
    console.log('\n💡 If you see errors above, those might be causing the transaction save failure.');

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Run the debug
debugDatabase().then(() => {
  console.log('\n✅ Debug completed');
}).catch(err => {
  console.error('❌ Debug failed:', err);
});
