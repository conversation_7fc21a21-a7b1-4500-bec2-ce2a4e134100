/**
 * Enhanced Confirmation Service for Phase 7.3
 * Implements inline buttons, edit flows, and reply threading with backend-synced dropdowns
 */

const EnhancedTransactionService = require('./enhanced-transaction-service.cjs');

class EnhancedConfirmationService {
  constructor(bot, supabase, i18nService) {
    this.bot = bot;
    this.supabase = supabase;
    this.i18nService = i18nService;
    this.enhancedTransactionService = new EnhancedTransactionService(supabase, i18nService);
    this.pendingTransactions = new Map();
    this.editSessions = new Map();
    this.errorContexts = new Map();

    // Validate dependencies
    if (!bot) {
      throw new Error('Bot instance is required for Enhanced Confirmation Service');
    }
    if (!supabase) {
      throw new Error('Supabase instance is required for Enhanced Confirmation Service');
    }
    if (!i18nService) {
      throw new Error('I18n Service is required for Enhanced Confirmation Service');
    }

    // Setup callback query handlers with delay to ensure bot is ready
    setTimeout(() => {
      this.setupCallbackHandlers();
    }, 100);

    console.log('✅ Enhanced Confirmation Service initialized');
  }

  /**
   * Setup callback query handlers for inline buttons
   */
  setupCallbackHandlers() {
    console.log('🔧 Enhanced Confirmation Service callback handlers ready');
    console.log('ℹ️ Using main bot callback handler integration');

    // The main bot will call our handleCallbackQuery method
    // No need to set up separate listeners here to avoid conflicts

    console.log('✅ Enhanced Confirmation Service ready for integration');
  }

  /**
   * Send enhanced transaction confirmation with inline buttons
   */
  async sendTransactionConfirmation(chatId, transactionData, language = 'en', replyToMessageId = null) {
    const transactionId = this.generateTransactionId();

    console.log('💾 STORING TRANSACTION:');
    console.log('   Transaction ID:', transactionId);
    console.log('   Chat ID:', chatId);
    console.log('   Transaction Data:', {
      amount: transactionData.amount,
      type: transactionData.type,
      category: transactionData.category,
      userId: transactionData.userId,
      language: language
    });

    // Store pending transaction
    this.pendingTransactions.set(transactionId, {
      ...transactionData,
      chatId,
      language,
      timestamp: Date.now(),
      originalMessageId: replyToMessageId
    });

    console.log('✅ Transaction stored. Total pending:', this.pendingTransactions.size);

    // Auto-expire after 5 minutes
    setTimeout(() => {
      this.pendingTransactions.delete(transactionId);
    }, 5 * 60 * 1000);

    console.log('🔧 Creating enhanced transaction form...');
    console.log('📋 Transaction data:', {
      amount: transactionData.amount,
      type: transactionData.type,
      category: transactionData.category,
      userId: transactionData.userId
    });

    let formMessage, inlineKeyboard;

    try {
      // Create enhanced interactive transaction form with backend dropdowns
      const enhancedTransaction = await this.enhancedTransactionService.createEnhancedTransactionForm(
        transactionData,
        transactionData.userId,
        language
      );

      console.log('✅ Enhanced transaction created:', {
        categories: enhancedTransaction.categories?.length || 0,
        paymentMethods: enhancedTransaction.paymentMethods?.length || 0,
        payment_method: enhancedTransaction.payment_method,
        location: enhancedTransaction.location
      });

      // Update stored transaction with enhanced data
      this.pendingTransactions.set(transactionId, {
        ...enhancedTransaction,
        chatId,
        language,
        timestamp: Date.now(),
        originalMessageId: replyToMessageId
      });

      formMessage = this.enhancedTransactionService.formatEnhancedInteractiveForm(enhancedTransaction, language);
      inlineKeyboard = this.enhancedTransactionService.createEnhancedFormKeyboard(transactionId, enhancedTransaction, language);

      console.log('✅ Enhanced form message and keyboard created');

    } catch (error) {
      console.error('❌ Error creating enhanced transaction form:', error);

      // Fallback to basic form
      formMessage = this.formatInteractiveForm(transactionData, language);
      inlineKeyboard = this.createInteractiveFormKeyboard(transactionId, transactionData, language);

      console.log('🔄 Using fallback basic form');
    }

    const options = {
      parse_mode: 'Markdown',
      reply_markup: inlineKeyboard
    };

    if (replyToMessageId) {
      options.reply_to_message_id = replyToMessageId;
    }

    const sentMessage = await this.bot.sendMessage(chatId, formMessage, options);
    
    // Store message ID for later editing
    const pendingTx = this.pendingTransactions.get(transactionId);
    if (pendingTx) {
      pendingTx.confirmationMessageId = sentMessage.message_id;
      this.pendingTransactions.set(transactionId, pendingTx);
    }

    return { transactionId, messageId: sentMessage.message_id };
  }

  /**
   * Handle all callback queries from inline buttons
   */
  async handleCallbackQuery(callbackQuery) {
    const { data, message, from } = callbackQuery;

    console.log('🎯 PROCESSING CALLBACK QUERY:');
    console.log('   Raw data:', data);
    console.log('   User ID:', from.id);
    console.log('   Username:', from.username);
    console.log('   Chat ID:', message?.chat?.id);

    if (!data) {
      console.error('❌ No callback data provided');
      return;
    }

    // Parse callback data correctly
    // Format: action_transactionId (where transactionId can contain underscores)
    const firstUnderscoreIndex = data.indexOf('_');
    if (firstUnderscoreIndex === -1) {
      console.error('❌ Invalid callback data format:', data);
      return;
    }

    const action = data.substring(0, firstUnderscoreIndex);
    const transactionId = data.substring(firstUnderscoreIndex + 1);

    console.log('🔍 Parsed callback data:', {
      action,
      transactionId,
      userId: from.id
    });

    // Get user language
    const language = this.i18nService.detectLanguage('', from.language_code);
    console.log('🌐 Detected language:', language);

    // Handle different action types
    if (action === 'confirm') {
      await this.handleConfirmTransaction(callbackQuery, transactionId, language);
    } else if (action === 'edit') {
      // Check if this is a field edit (format: edit_field_sessionId)
      if (transactionId.includes('_')) {
        const parts = transactionId.split('_');
        if (parts.length >= 2) {
          const field = parts[0];
          const sessionId = parts.slice(1).join('_');
          await this.handleEditField(callbackQuery, sessionId, field, language);
        } else {
          await this.handleEditTransaction(callbackQuery, transactionId, language);
        }
      } else {
        await this.handleEditTransaction(callbackQuery, transactionId, language);
      }
    } else if (action === 'discard') {
      await this.handleDiscardTransaction(callbackQuery, transactionId, language);
    } else if (action === 'reparse') {
      await this.handleReparseTransaction(callbackQuery, transactionId, language);
    } else if (action === 'suggestions') {
      await this.handleShowSuggestions(callbackQuery, transactionId, language);
    } else if (action === 'save') {
      await this.handleSaveEdit(callbackQuery, transactionId, language);
    } else if (action === 'cancel') {
      await this.handleCancelEdit(callbackQuery, transactionId, language);
    } else if (action === 'retry') {
      await this.handleRetryParse(callbackQuery, transactionId, language);
    } else if (action === 'manual') {
      await this.handleManualEntry(callbackQuery, transactionId, language);
    } else if (action === 'help') {
      await this.handleGetHelp(callbackQuery, transactionId, language);
    } else if (action === 'editfield') {
      // Handle field editing from interactive form
      const parts = transactionId.split('_');
      if (parts.length >= 2) {
        const field = parts[0];
        const actualTransactionId = parts.slice(1).join('_');
        await this.handleFieldEdit(callbackQuery, actualTransactionId, field, language);
      }
    } else if (action === 'switchtype') {
      await this.handleTypeSwitch(callbackQuery, transactionId, language);
    } else if (action === 'advanced') {
      await this.handleAdvancedOptions(callbackQuery, transactionId, language);
    } else if (action === 'dropdown') {
      // Handle dropdown selections
      // Format: dropdown_category_tx_123 or dropdown_payment_tx_123
      const parts = transactionId.split('_');
      if (parts.length >= 3) {
        const dropdownType = parts[0]; // 'category' or 'payment'
        const actualTransactionId = parts.slice(1).join('_'); // 'tx_123'
        console.log('🔍 Dropdown selection:', { dropdownType, actualTransactionId });
        await this.handleDropdownSelection(callbackQuery, actualTransactionId, dropdownType, language);
      } else {
        console.log('❌ Invalid dropdown callback format:', transactionId);
      }
    } else if (action === 'selectcat') {
      // Handle category selection
      const parts = transactionId.split('_');
      if (parts.length >= 2) {
        const categoryName = parts[0];
        const actualTransactionId = parts.slice(1).join('_');
        await this.handleCategorySelection(callbackQuery, actualTransactionId, categoryName, language);
      }
    } else if (action === 'selectpay') {
      // Handle payment method selection
      const parts = transactionId.split('_');
      if (parts.length >= 2) {
        const paymentMethod = parts[0];
        const actualTransactionId = parts.slice(1).join('_');
        await this.handlePaymentMethodSelection(callbackQuery, actualTransactionId, paymentMethod, language);
      }
    } else if (action === 'backtoform') {
      await this.handleBackToForm(callbackQuery, transactionId, language);
    } else if (action === 'catpage') {
      // Handle category pagination
      const parts = transactionId.split('_');
      if (parts.length >= 2) {
        const pageNumber = parseInt(parts[0]);
        const actualTransactionId = parts.slice(1).join('_');
        await this.handleCategoryPagination(callbackQuery, actualTransactionId, pageNumber, language);
      }
    } else if (action === 'catinfo') {
      // Handle category info (page indicator click)
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: 'Category selection page'
      });
    } else {
      console.log('❌ Unknown action:', action);
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('unknown_action', language),
        show_alert: true
      });
    }
  }

  /**
   * Handle transaction confirmation
   */
  async handleConfirmTransaction(callbackQuery, transactionId, language) {
    console.log('🔍 CONFIRM TRANSACTION DEBUG:');
    console.log('   Transaction ID:', transactionId);
    console.log('   Pending transactions count:', this.pendingTransactions.size);
    console.log('   Pending transaction IDs:', Array.from(this.pendingTransactions.keys()));

    const transaction = this.pendingTransactions.get(transactionId);
    console.log('   Found transaction:', !!transaction);

    if (!transaction) {
      console.log('❌ Transaction not found in pending transactions');
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    console.log('✅ Transaction found:', {
      amount: transaction.amount,
      type: transaction.type,
      category: transaction.category,
      userId: transaction.userId
    });

    try {
      // Save to database with enhanced fields
      const { data: savedTransaction, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: transaction.userId,
          amount: transaction.amount,
          type: transaction.type,
          category: transaction.category,
          description: transaction.description,
          payment_method: transaction.payment_method,
          location: transaction.location,
          source: transaction.type === 'income' ? transaction.category : 'telegram_bot',
          date: new Date().toISOString(),
          source_type: transaction.source || 'telegram_bot_enhanced',
          source_metadata: {
            telegram_user_id: callbackQuery.from.id.toString(),
            confirmation_method: 'enhanced_inline_button',
            original_message: transaction.originalMessage,
            confidence: transaction.confidence,
            language: language,
            ai_detected_payment_method: transaction.payment_method,
            ai_detected_location: transaction.location
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Update message with success
      const successMessage = this.formatSuccessMessage(savedTransaction, language);
      await this.editMessageWithResult(callbackQuery.message, successMessage);

      // Clean up
      this.pendingTransactions.delete(transactionId);

      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_saved', language)
      });

    } catch (error) {
      console.error('Transaction confirmation error:', error);
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('save_failed', language),
        show_alert: true
      });
    }
  }

  /**
   * Handle transaction editing
   */
  async handleEditTransaction(callbackQuery, transactionId, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Create edit session
    const editSessionId = this.generateEditSessionId();
    this.editSessions.set(editSessionId, {
      transactionId,
      originalTransaction: { ...transaction },
      currentTransaction: { ...transaction },
      chatId: callbackQuery.message.chat.id,
      userId: callbackQuery.from.id,
      language,
      step: 'field_selection'
    });

    const editMessage = this.formatEditMessage(transaction, language);
    const editKeyboard = this.createEditKeyboard(editSessionId, language);

    await this.bot.editMessageText(editMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: editKeyboard
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('edit_mode_activated', language)
    });
  }

  /**
   * Create edit keyboard for field selection
   */
  createEditKeyboard(editSessionId, language) {
    return {
      inline_keyboard: [
        [
          { 
            text: `💰 ${this.i18nService.t('edit_amount', language)}`, 
            callback_data: `edit_amount_${editSessionId}` 
          },
          { 
            text: `📂 ${this.i18nService.t('edit_category', language)}`, 
            callback_data: `edit_category_${editSessionId}` 
          }
        ],
        [
          { 
            text: `📝 ${this.i18nService.t('edit_description', language)}`, 
            callback_data: `edit_description_${editSessionId}` 
          },
          { 
            text: `📅 ${this.i18nService.t('edit_date', language)}`, 
            callback_data: `edit_date_${editSessionId}` 
          }
        ],
        [
          { 
            text: `💾 ${this.i18nService.t('save_changes', language)}`, 
            callback_data: `save_${editSessionId}` 
          },
          { 
            text: `❌ ${this.i18nService.t('cancel_edit', language)}`, 
            callback_data: `cancel_${editSessionId}` 
          }
        ]
      ]
    };
  }

  /**
   * Handle field editing
   */
  async handleEditField(callbackQuery, editSessionId, field, language) {
    const editSession = this.editSessions.get(editSessionId);
    if (!editSession) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('edit_session_expired', language),
        show_alert: true
      });
      return;
    }

    // Set up field editing
    editSession.editingField = field;
    editSession.step = 'awaiting_input';
    this.editSessions.set(editSessionId, editSession);

    const fieldPrompt = this.getFieldEditPrompt(field, editSession.currentTransaction, language);
    
    await this.bot.sendMessage(editSession.chatId, fieldPrompt, {
      parse_mode: 'Markdown',
      reply_markup: {
        force_reply: true,
        selective: true
      }
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('enter_new_value', language)
    });

    // Set up message listener for this edit session
    this.setupEditMessageListener(editSessionId);
  }

  /**
   * Setup message listener for edit input
   */
  setupEditMessageListener(editSessionId) {
    const editSession = this.editSessions.get(editSessionId);
    if (!editSession) return;

    const messageHandler = async (msg) => {
      if (msg.chat.id !== editSession.chatId || 
          msg.from.id !== editSession.userId ||
          !msg.reply_to_message) {
        return;
      }

      try {
        // Process the edit input
        const newValue = await this.processEditInput(
          editSession.editingField, 
          msg.text, 
          editSession.language
        );

        if (newValue.valid) {
          // Update transaction
          editSession.currentTransaction[editSession.editingField] = newValue.value;
          editSession.step = 'field_selection';
          delete editSession.editingField;
          this.editSessions.set(editSessionId, editSession);

          // Show updated edit interface
          const editMessage = this.formatEditMessage(editSession.currentTransaction, editSession.language);
          const editKeyboard = this.createEditKeyboard(editSessionId, editSession.language);

          await this.bot.sendMessage(editSession.chatId, editMessage, {
            parse_mode: 'Markdown',
            reply_markup: editKeyboard
          });

          // Remove this listener
          this.bot.removeListener('message', messageHandler);

        } else {
          // Invalid input, ask again
          await this.bot.sendMessage(editSession.chatId, 
            `❌ ${newValue.error}\n\n${this.getFieldEditPrompt(editSession.editingField, editSession.currentTransaction, editSession.language)}`,
            { parse_mode: 'Markdown' }
          );
        }

      } catch (error) {
        console.error('Edit input processing error:', error);
        await this.bot.sendMessage(editSession.chatId, 
          this.i18nService.t('edit_input_error', editSession.language)
        );
      }
    };

    this.bot.on('message', messageHandler);

    // Auto-cleanup after 2 minutes
    setTimeout(() => {
      this.bot.removeListener('message', messageHandler);
      if (this.editSessions.has(editSessionId)) {
        this.editSessions.delete(editSessionId);
      }
    }, 2 * 60 * 1000);
  }

  /**
   * Process edit input validation
   */
  async processEditInput(field, input, language) {
    switch (field) {
      case 'amount':
        const amount = parseFloat(input.replace(/[,₹]/g, ''));
        if (isNaN(amount) || amount <= 0) {
          return { 
            valid: false, 
            error: this.i18nService.t('invalid_amount', language) 
          };
        }
        return { valid: true, value: amount };

      case 'category':
        const validCategories = ['food', 'transport', 'shopping', 'entertainment', 'utilities', 'healthcare', 'other'];
        if (!validCategories.includes(input.toLowerCase())) {
          return { 
            valid: false, 
            error: this.i18nService.t('invalid_category', language) + ': ' + validCategories.join(', ')
          };
        }
        return { valid: true, value: input.toLowerCase() };

      case 'description':
        if (input.length < 1 || input.length > 200) {
          return { 
            valid: false, 
            error: this.i18nService.t('invalid_description_length', language) 
          };
        }
        return { valid: true, value: input };

      case 'date':
        const date = new Date(input);
        if (isNaN(date.getTime())) {
          return {
            valid: false,
            error: this.i18nService.t('invalid_date_format', language)
          };
        }
        return { valid: true, value: date.toISOString() };

      case 'location':
        if (input.length < 1 || input.length > 100) {
          return {
            valid: false,
            error: 'Location must be between 1 and 100 characters'
          };
        }
        return { valid: true, value: input.trim() };

      default:
        return { valid: false, error: 'Unknown field' };
    }
  }

  /**
   * Format confirmation message
   */
  formatConfirmationMessage(transaction, language) {
    const emoji = transaction.type === 'income' ? '💰' : '💸';
    const sign = transaction.type === 'income' ? '+' : '-';
    
    return `
${emoji} *${this.i18nService.t('ai_parsed_transaction', language)}*

**${this.i18nService.t('original_message', language)}:** "${transaction.originalMessage || transaction.description}"

**🧠 ${this.i18nService.t('parsed_data', language)}:**
💰 **${this.i18nService.t('amount', language)}:** ${sign}₹${transaction.amount.toLocaleString()}
📂 **${this.i18nService.t('type', language)}:** ${this.i18nService.t(transaction.type, language)}
🏷️ **${this.i18nService.t('category', language)}:** ${this.i18nService.t(transaction.category, language)}
📝 **${this.i18nService.t('description', language)}:** ${transaction.description}
🎯 **${this.i18nService.t('confidence', language)}:** ${Math.round((transaction.confidence || 0.8) * 100)}%

**💾 ${this.i18nService.t('ready_to_save', language)}**

${this.i18nService.t('choose_action_below', language)}
    `;
  }

  /**
   * Format edit message
   */
  formatEditMessage(transaction, language) {
    return `
✏️ *${this.i18nService.t('edit_transaction', language)}*

**${this.i18nService.t('current_values', language)}:**
💰 **${this.i18nService.t('amount', language)}:** ₹${transaction.amount.toLocaleString()}
📂 **${this.i18nService.t('category', language)}:** ${this.i18nService.t(transaction.category, language)}
📝 **${this.i18nService.t('description', language)}:** ${transaction.description}
📅 **${this.i18nService.t('date', language)}:** ${new Date(transaction.date || Date.now()).toLocaleDateString()}

${this.i18nService.t('select_field_to_edit', language)}
    `;
  }

  /**
   * Format success message
   */
  formatSuccessMessage(transaction, language) {
    const emoji = transaction.type === 'income' ? '💰' : '💸';
    
    return `
✅ *${this.i18nService.t('transaction_saved_successfully', language)}*

${emoji} **₹${transaction.amount.toLocaleString()}** - ${this.i18nService.t(transaction.category, language)}
📝 ${transaction.description}
🆔 **${this.i18nService.t('transaction_id', language)}:** ${transaction.id}
📅 **${this.i18nService.t('date', language)}:** ${new Date(transaction.date).toLocaleDateString()}

💾 *${this.i18nService.t('saved_to_account', language)}*

${this.i18nService.t('use_balance_command', language)}
    `;
  }

  /**
   * Get field edit prompt
   */
  getFieldEditPrompt(field, transaction, language) {
    const prompts = {
      amount: `💰 ${this.i18nService.t('enter_new_amount', language)}\n\n${this.i18nService.t('current', language)}: ₹${transaction.amount.toLocaleString()}\n${this.i18nService.t('example', language)}: 1500`,
      category: `📂 ${this.i18nService.t('enter_new_category', language)}\n\n${this.i18nService.t('current', language)}: ${transaction.category}\n${this.i18nService.t('available', language)}: food, transport, shopping, entertainment, utilities, healthcare, other`,
      description: `📝 ${this.i18nService.t('enter_new_description', language)}\n\n${this.i18nService.t('current', language)}: ${transaction.description}`,
      date: `📅 ${this.i18nService.t('enter_new_date', language)}\n\n${this.i18nService.t('current', language)}: ${new Date(transaction.date || Date.now()).toLocaleDateString()}\n${this.i18nService.t('format', language)}: YYYY-MM-DD`,
      location: `📍 **Edit Location**\n\n**Current:** ${transaction.location || 'Not specified'}\n\nEnter the location where this transaction occurred:\n\n**Examples:**\n• McDonald's\n• BigBazaar Mall\n• Home\n• Office\n• Starbucks Coffee\n• Local Market`
    };

    return prompts[field] || `${this.i18nService.t('enter_new_value', language)}:`;
  }

  /**
   * Edit message with result
   */
  async editMessageWithResult(message, newText) {
    try {
      await this.bot.editMessageText(newText, {
        chat_id: message.chat.id,
        message_id: message.message_id,
        parse_mode: 'Markdown'
      });
    } catch (error) {
      // If edit fails, send new message
      await this.bot.sendMessage(message.chat.id, newText, { parse_mode: 'Markdown' });
    }
  }

  /**
   * Handle transaction discard
   */
  async handleDiscardTransaction(callbackQuery, transactionId, language) {
    console.log('🗑️ Handling discard transaction:', transactionId);

    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Remove from pending transactions
    this.pendingTransactions.delete(transactionId);

    // Update message to show discarded status
    const discardMessage = `
❌ *${this.i18nService.t('transaction_discarded', language)}*

The transaction has been discarded and will not be saved.

💰 **Amount:** ₹${transaction.amount.toLocaleString()}
📂 **Category:** ${transaction.category}
📝 **Description:** ${transaction.description}

You can send a new message to log a different transaction.
    `;

    await this.bot.editMessageText(discardMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown'
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('transaction_discarded', language)
    });
  }

  /**
   * Handle transaction reparse
   */
  async handleReparseTransaction(callbackQuery, transactionId, language) {
    console.log('🔄 Handling reparse transaction:', transactionId);

    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Remove current transaction
    this.pendingTransactions.delete(transactionId);

    // Send message asking for new input
    const reparseMessage = `
🔄 *${this.i18nService.t('reparse_requested', language)}*

Please send your transaction message again, and I'll try to parse it differently.

**Previous attempt:**
💰 Amount: ₹${transaction.amount}
📂 Category: ${transaction.category}
📝 Description: ${transaction.description}

Send a new message like:
• "Spent 500 on lunch"
• "Paid 1200 for groceries"
• "Income 50000 salary"
    `;

    await this.bot.editMessageText(reparseMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown'
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('send_new_message', language)
    });
  }

  /**
   * Handle show suggestions
   */
  async handleShowSuggestions(callbackQuery, transactionId, language) {
    console.log('💡 Handling show suggestions:', transactionId);

    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Generate suggestions based on the transaction
    const suggestions = this.generateTransactionSuggestions(transaction, language);

    const suggestionsMessage = `
💡 *${this.i18nService.t('transaction_suggestions', language)}*

**Current Transaction:**
💰 Amount: ₹${transaction.amount}
📂 Category: ${transaction.category}
📝 Description: ${transaction.description}

**💡 Suggestions:**
${suggestions.join('\n')}

**Quick Actions:**
• Click ✅ to confirm as-is
• Click ✏️ to edit details
• Click ❌ to discard
    `;

    // Create new keyboard with suggestions applied
    const suggestionsKeyboard = {
      inline_keyboard: [
        [
          {
            text: `✅ ${this.i18nService.t('confirm', language)}`,
            callback_data: `confirm_${transactionId}`
          },
          {
            text: `✏️ ${this.i18nService.t('edit', language)}`,
            callback_data: `edit_${transactionId}`
          }
        ],
        [
          {
            text: `❌ ${this.i18nService.t('discard', language)}`,
            callback_data: `discard_${transactionId}`
          },
          {
            text: `🔄 ${this.i18nService.t('reparse', language)}`,
            callback_data: `reparse_${transactionId}`
          }
        ]
      ]
    };

    await this.bot.editMessageText(suggestionsMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: suggestionsKeyboard
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('suggestions_shown', language)
    });
  }

  /**
   * Generate transaction suggestions
   */
  generateTransactionSuggestions(transaction, language) {
    const suggestions = [];

    // Category suggestions
    if (transaction.category === 'other') {
      suggestions.push('🏷️ Consider using a more specific category like "food", "transport", or "entertainment"');
    }

    // Amount suggestions
    if (transaction.amount > 10000) {
      suggestions.push('💰 Large amount detected - double-check if this is correct');
    }

    // Description suggestions
    if (!transaction.description || transaction.description.length < 5) {
      suggestions.push('📝 Add more details to the description for better tracking');
    }

    // General suggestions
    suggestions.push('📊 This transaction will be saved to your real account');
    suggestions.push('🔍 You can view it later in the web app or with /recent');

    return suggestions;
  }

  /**
   * Format Interactive Transaction Form
   */
  formatInteractiveForm(transactionData, language) {
    const typeIcon = transactionData.type === 'income' ? '💰' : '💸';
    const typeText = transactionData.type === 'income' ? 'Income' : 'Expense';
    const confidenceBar = this.generateConfidenceBar(transactionData.confidence || 0.85);

    return `
📝 **Transaction Form** ${typeIcon}

${confidenceBar} *AI Confidence: ${Math.round((transactionData.confidence || 0.85) * 100)}%*

┌─────────────────────────────────────┐
│ **💰 Amount:** ₹${transactionData.amount.toLocaleString()}
│ **🏷️ Type:** ${typeText}
│ **📂 Category:** ${this.formatCategoryDisplay(transactionData.category)}
│ **📝 Description:** ${transactionData.description || 'Not specified'}
│ **📅 Date:** ${this.formatDateDisplay(transactionData.date)}
└─────────────────────────────────────┘

*Tap any field above to edit, or save as-is*

**💡 Quick Actions:**
• Edit any field by tapping the buttons below
• Switch between Expense/Income
• Add more details or change category
• Set custom date or recurring options
    `;
  }

  /**
   * Create Interactive Form Keyboard
   */
  createInteractiveFormKeyboard(transactionId, transactionData, language) {
    return {
      inline_keyboard: [
        // Row 1: Amount and Type
        [
          {
            text: `💰 ₹${transactionData.amount}`,
            callback_data: `editfield_amount_${transactionId}`
          },
          {
            text: `🔄 ${transactionData.type === 'income' ? 'Income' : 'Expense'}`,
            callback_data: `switchtype_${transactionId}`
          }
        ],
        // Row 2: Category and Description
        [
          {
            text: `📂 ${transactionData.category}`,
            callback_data: `editfield_category_${transactionId}`
          },
          {
            text: `📝 Description`,
            callback_data: `editfield_description_${transactionId}`
          }
        ],
        // Row 3: Date and Advanced Options
        [
          {
            text: `📅 ${this.formatDateDisplay(transactionData.date)}`,
            callback_data: `editfield_date_${transactionId}`
          },
          {
            text: `⚙️ Advanced`,
            callback_data: `advanced_${transactionId}`
          }
        ],
        // Row 4: Action Buttons
        [
          {
            text: `✅ Save Transaction`,
            callback_data: `confirm_${transactionId}`
          },
          {
            text: `❌ Cancel`,
            callback_data: `discard_${transactionId}`
          }
        ],
        // Row 5: Additional Options
        [
          {
            text: `🔄 Reparse`,
            callback_data: `reparse_${transactionId}`
          },
          {
            text: `💡 Suggestions`,
            callback_data: `suggestions_${transactionId}`
          }
        ]
      ]
    };
  }

  /**
   * Generate confidence bar visualization
   */
  generateConfidenceBar(confidence) {
    const percentage = Math.round(confidence * 100);
    const filledBars = Math.round(confidence * 10);
    const emptyBars = 10 - filledBars;

    const filled = '█'.repeat(filledBars);
    const empty = '░'.repeat(emptyBars);

    let color = '🔴'; // Red for low confidence
    if (percentage >= 70) color = '🟡'; // Yellow for medium
    if (percentage >= 85) color = '🟢'; // Green for high

    return `${color} [${filled}${empty}] ${percentage}%`;
  }

  /**
   * Format category display with emoji
   */
  formatCategoryDisplay(category) {
    const categoryEmojis = {
      food: '🍽️ Food',
      transport: '🚗 Transport',
      shopping: '🛒 Shopping',
      entertainment: '🎬 Entertainment',
      utilities: '⚡ Utilities',
      healthcare: '🏥 Healthcare',
      education: '📚 Education',
      travel: '✈️ Travel',
      other: '📦 Other',
      salary: '💼 Salary',
      freelance: '💻 Freelance',
      investment: '📈 Investment',
      business: '🏢 Business'
    };

    return categoryEmojis[category] || `📦 ${category}`;
  }

  /**
   * Format date display
   */
  formatDateDisplay(date) {
    if (!date) return 'Today';

    const transactionDate = new Date(date);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (transactionDate.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (transactionDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return transactionDate.toLocaleDateString();
    }
  }

  /**
   * Generate unique transaction ID
   */
  generateTransactionId() {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle save edit
   */
  async handleSaveEdit(callbackQuery, editSessionId, language) {
    console.log('💾 Handling save edit:', editSessionId);

    const editSession = this.editSessions.get(editSessionId);
    if (!editSession) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('edit_session_expired', language),
        show_alert: true
      });
      return;
    }

    try {
      // Save the edited transaction to database
      const { data: savedTransaction, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: editSession.currentTransaction.userId,
          amount: editSession.currentTransaction.amount,
          type: editSession.currentTransaction.type,
          category: editSession.currentTransaction.category,
          description: editSession.currentTransaction.description,
          source: editSession.currentTransaction.type === 'income' ? editSession.currentTransaction.category : 'telegram_bot',
          date: new Date().toISOString(),
          source_type: 'telegram_bot_enhanced_edited',
          source_metadata: {
            telegram_user_id: callbackQuery.from.id.toString(),
            confirmation_method: 'inline_button_edited',
            original_message: editSession.originalTransaction.originalMessage,
            edited_fields: this.getEditedFields(editSession.originalTransaction, editSession.currentTransaction),
            language: language
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Update message with success
      const successMessage = this.formatSuccessMessage(savedTransaction, language);
      await this.editMessageWithResult(callbackQuery.message, successMessage);

      // Clean up
      this.editSessions.delete(editSessionId);

      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_saved', language)
      });

    } catch (error) {
      console.error('Save edit error:', error);
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('save_failed', language),
        show_alert: true
      });
    }
  }

  /**
   * Handle cancel edit
   */
  async handleCancelEdit(callbackQuery, editSessionId, language) {
    console.log('❌ Handling cancel edit:', editSessionId);

    const editSession = this.editSessions.get(editSessionId);
    if (!editSession) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('edit_session_expired', language),
        show_alert: true
      });
      return;
    }

    // Remove edit session
    this.editSessions.delete(editSessionId);

    // Restore original confirmation message
    const originalTransaction = editSession.originalTransaction;
    const confirmationMessage = this.formatConfirmationMessage(originalTransaction, language);

    const inlineKeyboard = {
      inline_keyboard: [
        [
          {
            text: `✅ ${this.i18nService.t('confirm', language)}`,
            callback_data: `confirm_${editSession.transactionId}`
          },
          {
            text: `✏️ ${this.i18nService.t('edit', language)}`,
            callback_data: `edit_${editSession.transactionId}`
          }
        ],
        [
          {
            text: `❌ ${this.i18nService.t('discard', language)}`,
            callback_data: `discard_${editSession.transactionId}`
          },
          {
            text: `🔄 ${this.i18nService.t('reparse', language)}`,
            callback_data: `reparse_${editSession.transactionId}`
          }
        ],
        [
          {
            text: `💡 ${this.i18nService.t('suggestions', language)}`,
            callback_data: `suggestions_${editSession.transactionId}`
          }
        ]
      ]
    };

    await this.bot.editMessageText(confirmationMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: inlineKeyboard
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('edit_cancelled', language)
    });
  }

  /**
   * Handle retry parse
   */
  async handleRetryParse(callbackQuery, errorId, language) {
    console.log('🔄 Handling retry parse:', errorId);

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('send_new_message', language)
    });

    await this.bot.sendMessage(callbackQuery.message.chat.id,
      `🔄 Please send your transaction message again, and I'll try to parse it.`
    );
  }

  /**
   * Handle manual entry
   */
  async handleManualEntry(callbackQuery, errorId, language) {
    console.log('✏️ Handling manual entry:', errorId);

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('use_manual_commands', language)
    });

    const manualMessage = `
✏️ *Manual Transaction Entry*

Use these commands to log transactions manually:

**For Expenses:**
\`/expense <amount> <category> [description]\`
Example: \`/expense 500 food Lunch at restaurant\`

**For Income:**
\`/income <amount> <source> [description]\`
Example: \`/income 50000 salary Monthly salary\`

**Available Categories:**
• food, transport, entertainment, shopping
• utilities, healthcare, education, travel, other

**Available Income Sources:**
• salary, freelance, investment, business, other
    `;

    await this.bot.sendMessage(callbackQuery.message.chat.id, manualMessage, {
      parse_mode: 'Markdown'
    });
  }

  /**
   * Handle get help
   */
  async handleGetHelp(callbackQuery, errorId, language) {
    console.log('❓ Handling get help:', errorId);

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: this.i18nService.t('help_sent', language)
    });

    await this.bot.sendMessage(callbackQuery.message.chat.id,
      `❓ Need help? Use /help to see all available commands and features.`
    );
  }

  /**
   * Get edited fields comparison
   */
  getEditedFields(original, edited) {
    const changes = {};

    if (original.amount !== edited.amount) {
      changes.amount = { from: original.amount, to: edited.amount };
    }
    if (original.category !== edited.category) {
      changes.category = { from: original.category, to: edited.category };
    }
    if (original.description !== edited.description) {
      changes.description = { from: original.description, to: edited.description };
    }

    return changes;
  }

  /**
   * Handle field editing from interactive form
   */
  async handleFieldEdit(callbackQuery, transactionId, field, language) {
    console.log('✏️ Handling field edit:', { transactionId, field });

    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // For location field, set up direct text input
    if (field === 'location') {
      await this.handleLocationInput(callbackQuery, transactionId, transaction, language);
      return;
    }

    // Create edit session for other fields
    const editSessionId = this.generateEditSessionId();
    this.editSessions.set(editSessionId, {
      transactionId,
      originalTransaction: { ...transaction },
      currentTransaction: { ...transaction },
      chatId: callbackQuery.message.chat.id,
      userId: callbackQuery.from.id,
      language,
      editingField: field,
      step: 'awaiting_input'
    });

    const fieldPrompt = this.getFieldEditPrompt(field, transaction, language);

    await this.bot.sendMessage(callbackQuery.message.chat.id, fieldPrompt, {
      parse_mode: 'Markdown',
      reply_markup: {
        force_reply: true,
        selective: true
      }
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: `✏️ Enter new ${field}`
    });

    // Set up message listener for this edit session
    this.setupEditMessageListener(editSessionId);
  }

  /**
   * Handle type switching (Expense ↔ Income)
   */
  async handleTypeSwitch(callbackQuery, transactionId, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Switch type
    transaction.type = transaction.type === 'income' ? 'expense' : 'income';

    // Update category if needed
    if (transaction.type === 'income' && !['salary', 'freelance', 'investment', 'business'].includes(transaction.category)) {
      transaction.category = 'salary'; // Default income category
    } else if (transaction.type === 'expense' && ['salary', 'freelance', 'investment', 'business'].includes(transaction.category)) {
      transaction.category = 'other'; // Default expense category
    }

    this.pendingTransactions.set(transactionId, transaction);

    // Update the form
    const formMessage = this.formatInteractiveForm(transaction, language);
    const inlineKeyboard = this.createInteractiveFormKeyboard(transactionId, transaction, language);

    await this.bot.editMessageText(formMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: inlineKeyboard
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: `🔄 Switched to ${transaction.type}`
    });
  }

  /**
   * Handle advanced options
   */
  async handleAdvancedOptions(callbackQuery, transactionId, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    const advancedMessage = `
⚙️ **Advanced Options**

**Current Transaction:**
💰 Amount: ₹${transaction.amount}
🏷️ Type: ${transaction.type}
📂 Category: ${transaction.category}

**Available Options:**
• 🔄 Set as recurring transaction
• 📊 Add to budget tracking
• 🏷️ Add custom tags
• 📸 Attach receipt (coming soon)
• 🔗 Link to project/goal

*More advanced features coming soon!*
    `;

    const advancedKeyboard = {
      inline_keyboard: [
        [
          {
            text: `🔄 Make Recurring`,
            callback_data: `recurring_${transactionId}`
          },
          {
            text: `📊 Budget Track`,
            callback_data: `budget_${transactionId}`
          }
        ],
        [
          {
            text: `🏷️ Add Tags`,
            callback_data: `tags_${transactionId}`
          },
          {
            text: `🔗 Link Goal`,
            callback_data: `goal_${transactionId}`
          }
        ],
        [
          {
            text: `⬅️ Back to Form`,
            callback_data: `backtoform_${transactionId}`
          }
        ]
      ]
    };

    await this.bot.editMessageText(advancedMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: advancedKeyboard
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: '⚙️ Advanced options'
    });
  }

  /**
   * Handle dropdown selection (category or payment method)
   */
  async handleDropdownSelection(callbackQuery, transactionId, dropdownType, language) {
    console.log('🔽 Handling dropdown selection:', { transactionId, dropdownType });

    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      console.log('❌ Transaction not found:', transactionId);
      console.log('📋 Available transactions:', Array.from(this.pendingTransactions.keys()));
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    console.log('✅ Transaction found:', {
      amount: transaction.amount,
      type: transaction.type,
      category: transaction.category
    });

    if (dropdownType === 'category') {
      // Show category dropdown
      const categories = await this.enhancedTransactionService.getCategories(transaction.userId, transaction.type);
      const categoryKeyboard = this.enhancedTransactionService.createCategoryDropdown(categories, transactionId, language);

      const categoryMessage = `
📂 **Select Category**

Choose a category for your ${transaction.type}:

**Current:** ${this.enhancedTransactionService.formatCategoryDisplay(transaction.category)}
**Amount:** ₹${transaction.amount}
**Type:** ${transaction.type}
      `;

      await this.bot.editMessageText(categoryMessage, {
        chat_id: callbackQuery.message.chat.id,
        message_id: callbackQuery.message.message_id,
        parse_mode: 'Markdown',
        reply_markup: categoryKeyboard
      });

    } else if (dropdownType === 'payment') {
      // Show payment method dropdown
      const paymentMethods = await this.enhancedTransactionService.getPaymentMethods(transaction.userId);
      const paymentKeyboard = this.enhancedTransactionService.createPaymentMethodDropdown(paymentMethods, transactionId, language);

      const paymentMessage = `
💳 **Select Payment Method**

Choose how you paid for this transaction:

**Current:** ${this.enhancedTransactionService.formatPaymentMethodDisplay(transaction.payment_method)}
**Amount:** ₹${transaction.amount}
**Category:** ${transaction.category}
      `;

      await this.bot.editMessageText(paymentMessage, {
        chat_id: callbackQuery.message.chat.id,
        message_id: callbackQuery.message.message_id,
        parse_mode: 'Markdown',
        reply_markup: paymentKeyboard
      });
    }

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: `📋 Select ${dropdownType}`
    });
  }

  /**
   * Handle category selection from dropdown
   */
  async handleCategorySelection(callbackQuery, transactionId, categoryName, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Update transaction category
    transaction.category = categoryName;
    this.pendingTransactions.set(transactionId, transaction);

    // Return to enhanced form
    await this.showEnhancedForm(callbackQuery, transactionId, transaction, language);

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: `✅ Category updated to ${categoryName}`
    });
  }

  /**
   * Handle payment method selection from dropdown
   */
  async handlePaymentMethodSelection(callbackQuery, transactionId, paymentMethod, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Update transaction payment method
    transaction.payment_method = paymentMethod;
    this.pendingTransactions.set(transactionId, transaction);

    // Return to enhanced form
    await this.showEnhancedForm(callbackQuery, transactionId, transaction, language);

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: `✅ Payment method updated to ${paymentMethod}`
    });
  }

  /**
   * Handle back to form navigation
   */
  async handleBackToForm(callbackQuery, transactionId, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    await this.showEnhancedForm(callbackQuery, transactionId, transaction, language);

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: '⬅️ Back to form'
    });
  }

  /**
   * Handle category pagination
   */
  async handleCategoryPagination(callbackQuery, transactionId, pageNumber, language) {
    const transaction = this.pendingTransactions.get(transactionId);
    if (!transaction) {
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: this.i18nService.t('transaction_expired', language),
        show_alert: true
      });
      return;
    }

    // Get categories and show the requested page
    const categories = await this.enhancedTransactionService.getCategories(transaction.userId, transaction.type);
    const categoryKeyboard = this.enhancedTransactionService.createCategoryDropdown(categories, transactionId, language, pageNumber);

    const categoryMessage = `
📂 **Select Category** (Page ${pageNumber + 1})

Choose a category for your ${transaction.type}:

**Current:** ${this.enhancedTransactionService.formatCategoryDisplay(transaction.category)}
**Amount:** ₹${transaction.amount}
**Type:** ${transaction.type}
    `;

    await this.bot.editMessageText(categoryMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: categoryKeyboard
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: `📋 Page ${pageNumber + 1}`
    });
  }

  /**
   * Show enhanced form with updated data
   */
  async showEnhancedForm(callbackQuery, transactionId, transaction, language) {
    const formMessage = this.enhancedTransactionService.formatEnhancedInteractiveForm(transaction, language);
    const inlineKeyboard = this.enhancedTransactionService.createEnhancedFormKeyboard(transactionId, transaction, language);

    await this.bot.editMessageText(formMessage, {
      chat_id: callbackQuery.message.chat.id,
      message_id: callbackQuery.message.message_id,
      parse_mode: 'Markdown',
      reply_markup: inlineKeyboard
    });
  }

  /**
   * Handle location input with direct text messaging
   */
  async handleLocationInput(callbackQuery, transactionId, transaction, language) {
    console.log('📍 Setting up location input for transaction:', transactionId);

    // Create location input session
    const locationSessionId = this.generateEditSessionId();
    this.editSessions.set(locationSessionId, {
      transactionId,
      originalTransaction: { ...transaction },
      currentTransaction: { ...transaction },
      chatId: callbackQuery.message.chat.id,
      userId: callbackQuery.from.id,
      language,
      editingField: 'location',
      step: 'awaiting_location_input',
      originalMessageId: callbackQuery.message.message_id
    });

    // Send location input prompt
    const locationPrompt = `
📍 **Enter Location**

**Current Location:** ${transaction.location || 'Not specified'}

Please type the location where this transaction occurred:

**Examples:**
• McDonald's
• BigBazaar Mall
• Home
• Office
• Starbucks Coffee
• Local Market

*Just type your message below...*
    `;

    await this.bot.sendMessage(callbackQuery.message.chat.id, locationPrompt, {
      parse_mode: 'Markdown',
      reply_markup: {
        force_reply: true,
        selective: true,
        input_field_placeholder: 'Enter location...'
      }
    });

    await this.bot.answerCallbackQuery(callbackQuery.id, {
      text: '📍 Type the location in chat'
    });

    // Set up location message listener
    this.setupLocationMessageListener(locationSessionId);
  }

  /**
   * Set up location message listener
   */
  setupLocationMessageListener(locationSessionId) {
    console.log('👂 Setting up location message listener:', locationSessionId);

    const locationSession = this.editSessions.get(locationSessionId);
    if (!locationSession) {
      console.log('❌ Location session not found:', locationSessionId);
      return;
    }

    const messageHandler = async (msg) => {
      // Only process messages from the correct user and chat
      if (msg.from.id !== locationSession.userId ||
          msg.chat.id !== locationSession.chatId ||
          !msg.text) {
        return;
      }

      console.log('📍 Received location input:', msg.text);

      try {
        // Validate location input
        const locationText = msg.text.trim();
        if (locationText.length < 1 || locationText.length > 100) {
          await this.bot.sendMessage(locationSession.chatId,
            '❌ Location must be between 1 and 100 characters. Please try again:',
            { parse_mode: 'Markdown' }
          );
          return;
        }

        // Update transaction with location
        const transaction = this.pendingTransactions.get(locationSession.transactionId);
        if (!transaction) {
          await this.bot.sendMessage(locationSession.chatId,
            '❌ Transaction expired. Please start over.',
            { parse_mode: 'Markdown' }
          );
          this.editSessions.delete(locationSessionId);
          this.bot.removeListener('message', messageHandler);
          return;
        }

        // Update location
        transaction.location = locationText;
        this.pendingTransactions.set(locationSession.transactionId, transaction);

        console.log('✅ Location updated:', { transactionId: locationSession.transactionId, location: locationText });

        // Show updated enhanced form
        const formMessage = this.enhancedTransactionService.formatEnhancedInteractiveForm(transaction, locationSession.language);
        const inlineKeyboard = this.enhancedTransactionService.createEnhancedFormKeyboard(locationSession.transactionId, transaction, locationSession.language);

        // Update the original form message
        await this.bot.editMessageText(formMessage, {
          chat_id: locationSession.chatId,
          message_id: locationSession.originalMessageId,
          parse_mode: 'Markdown',
          reply_markup: inlineKeyboard
        });

        // Send confirmation message
        await this.bot.sendMessage(locationSession.chatId,
          `✅ Location updated to: **${locationText}**`,
          { parse_mode: 'Markdown' }
        );

        // Clean up
        this.editSessions.delete(locationSessionId);
        this.bot.removeListener('message', messageHandler);

      } catch (error) {
        console.error('❌ Error processing location input:', error);
        await this.bot.sendMessage(locationSession.chatId,
          '❌ Error updating location. Please try again.',
          { parse_mode: 'Markdown' }
        );
      }
    };

    // Add message listener
    this.bot.on('message', messageHandler);

    // Auto-cleanup after 2 minutes
    setTimeout(() => {
      console.log('⏰ Location input session timeout:', locationSessionId);
      this.bot.removeListener('message', messageHandler);
      if (this.editSessions.has(locationSessionId)) {
        this.editSessions.delete(locationSessionId);
      }
    }, 2 * 60 * 1000);
  }

  /**
   * Generate unique edit session ID
   */
  generateEditSessionId() {
    return `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup expired sessions
   */
  cleanup() {
    const now = Date.now();
    const expireTime = 5 * 60 * 1000; // 5 minutes

    // Clean up pending transactions
    for (const [id, transaction] of this.pendingTransactions.entries()) {
      if (now - transaction.timestamp > expireTime) {
        this.pendingTransactions.delete(id);
      }
    }

    // Clean up edit sessions
    for (const [id, session] of this.editSessions.entries()) {
      if (now - session.timestamp > expireTime) {
        this.editSessions.delete(id);
      }
    }

    // Clean up error contexts
    for (const [id, context] of this.errorContexts.entries()) {
      if (now - context.timestamp > expireTime) {
        this.errorContexts.delete(id);
      }
    }
  }
}

module.exports = EnhancedConfirmationService;
