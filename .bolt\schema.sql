-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.ab_test_results (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  test_id uuid NOT NULL,
  variant_id uuid NOT NULL,
  user_id uuid NOT NULL,
  notification_id uuid,
  assigned_at timestamp with time zone DEFAULT now(),
  converted boolean DEFAULT false,
  conversion_event character varying,
  conversion_at timestamp with time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT ab_test_results_pkey PRIMARY KEY (id),
  CONSTRAINT ab_test_results_variant_id_fkey FOREIGN KEY (variant_id) REFERENCES public.ab_test_variants(id),
  CONSTRAINT ab_test_results_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT ab_test_results_test_id_fkey FOREIGN KEY (test_id) REFERENCES public.ab_tests(id)
);
CREATE TABLE public.ab_test_variants (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  test_id uuid NOT NULL,
  name character varying NOT NULL,
  description text,
  is_control boolean DEFAULT false,
  configuration jsonb NOT NULL DEFAULT '{}'::jsonb,
  participants integer DEFAULT 0,
  conversions integer DEFAULT 0,
  conversion_rate numeric DEFAULT 0.0000,
  statistical_power numeric DEFAULT 0.00,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ab_test_variants_pkey PRIMARY KEY (id),
  CONSTRAINT ab_test_variants_test_id_fkey FOREIGN KEY (test_id) REFERENCES public.ab_tests(id)
);
CREATE TABLE public.ab_tests (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['content'::character varying, 'timing'::character varying, 'delivery_method'::character varying, 'frequency'::character varying, 'design'::character varying]::text[])),
  status character varying NOT NULL DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'running'::character varying, 'paused'::character varying, 'completed'::character varying, 'cancelled'::character varying]::text[])),
  start_date timestamp with time zone,
  end_date timestamp with time zone,
  target_users character varying NOT NULL DEFAULT 'all'::character varying CHECK (target_users::text = ANY (ARRAY['all'::character varying, 'segment'::character varying, 'percentage'::character varying]::text[])),
  target_criteria jsonb DEFAULT '{}'::jsonb,
  traffic_split integer NOT NULL DEFAULT 50 CHECK (traffic_split >= 0 AND traffic_split <= 100),
  success_metrics ARRAY DEFAULT '{conversion_rate}'::text[],
  statistical_significance numeric DEFAULT 95.0,
  confidence_level numeric DEFAULT 95.0,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ab_tests_pkey PRIMARY KEY (id),
  CONSTRAINT ab_tests_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.admin_audit_log (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  admin_id uuid,
  action text NOT NULL,
  target_type text,
  target_id text,
  details jsonb DEFAULT '{}'::jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT admin_audit_log_pkey PRIMARY KEY (id),
  CONSTRAINT admin_audit_log_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES auth.users(id)
);
CREATE TABLE public.admin_ip_whitelist (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  ip_address inet NOT NULL UNIQUE,
  ip_range text,
  description text NOT NULL,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  is_active boolean NOT NULL DEFAULT true,
  last_used_at timestamp with time zone,
  usage_count integer NOT NULL DEFAULT 0,
  CONSTRAINT admin_ip_whitelist_pkey PRIMARY KEY (id),
  CONSTRAINT admin_ip_whitelist_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.admin_sessions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  admin_id uuid,
  session_token text NOT NULL UNIQUE,
  verification_code text,
  is_verified boolean DEFAULT false,
  expires_at timestamp with time zone NOT NULL,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  last_activity timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  logout_at timestamp with time zone,
  CONSTRAINT admin_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT admin_sessions_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES auth.users(id)
);
CREATE TABLE public.admin_users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid UNIQUE,
  email text NOT NULL UNIQUE,
  role text NOT NULL DEFAULT 'admin'::text CHECK (role = ANY (ARRAY['super_admin'::text, 'admin'::text, 'moderator'::text])),
  permissions jsonb DEFAULT '[]'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  last_login timestamp with time zone,
  login_count integer DEFAULT 0,
  CONSTRAINT admin_users_pkey PRIMARY KEY (id),
  CONSTRAINT admin_users_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT admin_users_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.ai_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  type character varying NOT NULL,
  title character varying NOT NULL,
  description text NOT NULL,
  impact character varying DEFAULT 'medium'::character varying CHECK (impact::text = ANY (ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying]::text[])),
  confidence numeric DEFAULT 0.5 CHECK (confidence >= 0::numeric AND confidence <= 1::numeric),
  actionable boolean DEFAULT true,
  actions jsonb DEFAULT '[]'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone,
  CONSTRAINT ai_insights_pkey PRIMARY KEY (id),
  CONSTRAINT ai_insights_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.announcement_analytics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  announcement_id uuid,
  user_id uuid,
  action text NOT NULL CHECK (action = ANY (ARRAY['view'::text, 'click'::text, 'dismiss'::text])),
  user_agent text,
  ip_address inet,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT announcement_analytics_pkey PRIMARY KEY (id),
  CONSTRAINT announcement_analytics_announcement_id_fkey FOREIGN KEY (announcement_id) REFERENCES public.announcements(id)
);
CREATE TABLE public.announcement_campaigns (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  announcement_id uuid,
  target_segments ARRAY DEFAULT '{}'::uuid[],
  target_user_roles ARRAY DEFAULT '{}'::text[],
  target_subscription_tiers ARRAY DEFAULT '{}'::text[],
  geographic_targets ARRAY DEFAULT '{}'::text[],
  behavioral_triggers jsonb DEFAULT '{}'::jsonb,
  schedule_type text DEFAULT 'immediate'::text CHECK (schedule_type = ANY (ARRAY['immediate'::text, 'scheduled'::text, 'triggered'::text])),
  scheduled_for timestamp with time zone,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT announcement_campaigns_pkey PRIMARY KEY (id),
  CONSTRAINT announcement_campaigns_announcement_id_fkey FOREIGN KEY (announcement_id) REFERENCES public.announcements(id)
);
CREATE TABLE public.announcement_targets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  announcement_id uuid,
  target_type text NOT NULL CHECK (target_type = ANY (ARRAY['user_role'::text, 'user_group'::text, 'user_id'::text, 'subscription_tier'::text])),
  target_value text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT announcement_targets_pkey PRIMARY KEY (id),
  CONSTRAINT announcement_targets_announcement_id_fkey FOREIGN KEY (announcement_id) REFERENCES public.announcements(id)
);
CREATE TABLE public.announcements (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  type text DEFAULT 'info'::text CHECK (type = ANY (ARRAY['info'::text, 'warning'::text, 'success'::text, 'error'::text])),
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  target_audience text DEFAULT 'all'::text,
  priority text DEFAULT 'normal'::text CHECK (priority = ANY (ARRAY['low'::text, 'normal'::text, 'high'::text, 'urgent'::text])),
  start_date timestamp with time zone,
  end_date timestamp with time zone,
  auto_dismiss_after integer DEFAULT 0,
  view_count integer DEFAULT 0,
  click_count integer DEFAULT 0,
  dismiss_count integer DEFAULT 0,
  created_by_name text,
  tags ARRAY,
  CONSTRAINT announcements_pkey PRIMARY KEY (id)
);
CREATE TABLE public.api_usage_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  endpoint character varying NOT NULL,
  method character varying NOT NULL,
  user_id uuid,
  ip_address inet,
  user_agent text,
  request_size_bytes integer,
  response_size_bytes integer,
  response_time_ms integer,
  status_code integer,
  success boolean,
  error_message text,
  rate_limited boolean DEFAULT false,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT api_usage_logs_pkey PRIMARY KEY (id),
  CONSTRAINT api_usage_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.approval_requests (
  id text NOT NULL,
  group_id text NOT NULL,
  requester_id uuid NOT NULL,
  transaction_data jsonb NOT NULL,
  status text NOT NULL DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'approved'::text, 'rejected'::text, 'expired'::text])),
  approved_by uuid,
  approved_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  CONSTRAINT approval_requests_pkey PRIMARY KEY (id),
  CONSTRAINT approval_requests_requester_id_fkey FOREIGN KEY (requester_id) REFERENCES auth.users(id),
  CONSTRAINT approval_requests_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES auth.users(id),
  CONSTRAINT approval_requests_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.family_groups(id)
);
CREATE TABLE public.automation_rules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  rule_name character varying NOT NULL,
  rule_type character varying NOT NULL,
  priority integer DEFAULT 1,
  status character varying DEFAULT 'active'::character varying CHECK (status::text = ANY (ARRAY['active'::character varying, 'inactive'::character varying, 'paused'::character varying]::text[])),
  conditions jsonb DEFAULT '[]'::jsonb,
  actions jsonb DEFAULT '[]'::jsonb,
  learning_weight numeric DEFAULT 0.5,
  success_rate numeric DEFAULT 0,
  confidence_score numeric DEFAULT 0,
  execution_count integer DEFAULT 0,
  last_executed timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT automation_rules_pkey PRIMARY KEY (id),
  CONSTRAINT automation_rules_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.bot_interactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  telegram_user_id uuid,
  telegram_id bigint,
  command text,
  message_text text,
  success boolean DEFAULT true,
  error_message text,
  processing_time_ms integer,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT bot_interactions_pkey PRIMARY KEY (id),
  CONSTRAINT bot_interactions_telegram_user_id_fkey FOREIGN KEY (telegram_user_id) REFERENCES public.telegram_users(id)
);
CREATE TABLE public.budget_import_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  import_type text NOT NULL,
  file_name text,
  status text NOT NULL CHECK (status = ANY (ARRAY['pending'::text, 'success'::text, 'failed'::text])),
  results jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT budget_import_logs_pkey PRIMARY KEY (id),
  CONSTRAINT budget_import_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.budget_scenarios (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  name text NOT NULL,
  description text,
  modifications jsonb NOT NULL,
  results jsonb,
  saved boolean DEFAULT false,
  timeframe text DEFAULT 'current_month'::text CHECK (timeframe = ANY (ARRAY['current_month'::text, 'next_month'::text, 'quarter'::text, 'year'::text])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT budget_scenarios_pkey PRIMARY KEY (id),
  CONSTRAINT budget_scenarios_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.budget_stress_tests (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  test_type text NOT NULL CHECK (test_type = ANY (ARRAY['income_loss'::text, 'unexpected_expense'::text, 'market_volatility'::text, 'seasonal_spike'::text])),
  parameters jsonb NOT NULL,
  results jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT budget_stress_tests_pkey PRIMARY KEY (id),
  CONSTRAINT budget_stress_tests_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.budget_subcategories (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  budget_id uuid,
  name text NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  envelope_config jsonb DEFAULT '{"icon": "folder", "color": "#10B981", "priority": 1}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT budget_subcategories_pkey PRIMARY KEY (id),
  CONSTRAINT budget_subcategories_budget_id_fkey FOREIGN KEY (budget_id) REFERENCES public.budgets(id)
);
CREATE TABLE public.budget_templates (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  name text NOT NULL,
  description text,
  categories jsonb NOT NULL,
  target_income numeric,
  is_default boolean DEFAULT false,
  behavioral_insights ARRAY,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  usage_count integer DEFAULT 0,
  CONSTRAINT budget_templates_pkey PRIMARY KEY (id),
  CONSTRAINT budget_templates_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.budgets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  category text NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  period_start date,
  period_end date,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  period_type text DEFAULT 'monthly'::text CHECK (period_type = ANY (ARRAY['weekly'::text, 'monthly'::text, 'quarterly'::text, 'yearly'::text, 'custom'::text])),
  template_id uuid,
  rollover_settings jsonb DEFAULT '{"type": "unused_only", "enabled": false, "maxRolloverPercentage": 20}'::jsonb,
  envelope_config jsonb DEFAULT '{"icon": "wallet", "color": "#3B82F6", "priority": 1}'::jsonb,
  allow_subcategory_overflow boolean DEFAULT true,
  CONSTRAINT budgets_pkey PRIMARY KEY (id),
  CONSTRAINT budgets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.bulk_operations (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  operation_type text NOT NULL,
  operation_name text,
  description text,
  status text NOT NULL DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'running'::text, 'completed'::text, 'failed'::text, 'cancelled'::text])),
  total_users integer DEFAULT 0,
  processed_users integer DEFAULT 0,
  successful_users integer DEFAULT 0,
  failed_users integer DEFAULT 0,
  error_details jsonb,
  file_url text,
  filters jsonb,
  initiated_by uuid NOT NULL,
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT bulk_operations_pkey PRIMARY KEY (id),
  CONSTRAINT bulk_operations_initiated_by_fkey FOREIGN KEY (initiated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.categories (
  id uuid NOT NULL,
  name text NOT NULL,
  type text NOT NULL CHECK (type = ANY (ARRAY['income'::text, 'expense'::text])),
  color text,
  icon text,
  is_default boolean NOT NULL DEFAULT false,
  user_id uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT categories_pkey PRIMARY KEY (id),
  CONSTRAINT categories_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.category_standardization (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  category_name character varying NOT NULL,
  category_type character varying NOT NULL,
  parent_category_id uuid,
  category_level integer DEFAULT 1,
  display_order integer DEFAULT 0,
  icon_name character varying,
  color_code character varying,
  description text,
  keywords ARRAY,
  is_system_category boolean DEFAULT true,
  is_active boolean DEFAULT true,
  is_deletable boolean DEFAULT true,
  usage_count integer DEFAULT 0,
  created_by uuid,
  updated_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT category_standardization_pkey PRIMARY KEY (id),
  CONSTRAINT category_standardization_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT category_standardization_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT category_standardization_parent_category_id_fkey FOREIGN KEY (parent_category_id) REFERENCES public.category_standardization(id)
);
CREATE TABLE public.collaboration_invites (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  sender_id uuid,
  sender_email text NOT NULL,
  sender_name text NOT NULL,
  recipient_email text NOT NULL,
  resource_type text NOT NULL CHECK (resource_type = ANY (ARRAY['household'::text, 'budget'::text, 'goal'::text])),
  resource_id uuid NOT NULL,
  resource_name text NOT NULL,
  permission text NOT NULL CHECK (permission = ANY (ARRAY['view'::text, 'edit'::text, 'admin'::text])),
  status text NOT NULL DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'accepted'::text, 'declined'::text])),
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  CONSTRAINT collaboration_invites_pkey PRIMARY KEY (id),
  CONSTRAINT collaboration_invites_sender_id_fkey FOREIGN KEY (sender_id) REFERENCES auth.users(id)
);
CREATE TABLE public.collaborators (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  email text NOT NULL,
  name text NOT NULL,
  resource_type text NOT NULL CHECK (resource_type = ANY (ARRAY['household'::text, 'budget'::text, 'goal'::text])),
  resource_id uuid NOT NULL,
  permission text NOT NULL CHECK (permission = ANY (ARRAY['view'::text, 'edit'::text, 'admin'::text])),
  created_at timestamp with time zone DEFAULT now(),
  last_active_at timestamp with time zone DEFAULT now(),
  CONSTRAINT collaborators_pkey PRIMARY KEY (id),
  CONSTRAINT collaborators_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.comments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  user_name text NOT NULL,
  resource_type text NOT NULL CHECK (resource_type = ANY (ARRAY['transaction'::text, 'budget'::text, 'goal'::text])),
  resource_id uuid NOT NULL,
  content text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT comments_pkey PRIMARY KEY (id),
  CONSTRAINT comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.compliance_reports (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  report_name character varying NOT NULL,
  compliance_framework character varying NOT NULL,
  report_type character varying NOT NULL,
  reporting_period_start date NOT NULL,
  reporting_period_end date NOT NULL,
  findings jsonb,
  compliance_score numeric,
  status character varying DEFAULT 'draft'::character varying,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT compliance_reports_pkey PRIMARY KEY (id),
  CONSTRAINT compliance_reports_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.conversation_contexts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  conversation_history jsonb DEFAULT '[]'::jsonb,
  current_emotion text,
  context_data jsonb DEFAULT '{}'::jsonb,
  message_count integer DEFAULT 0,
  last_interaction timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT conversation_contexts_pkey PRIMARY KEY (id),
  CONSTRAINT conversation_contexts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.credit_card_comparison_features (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  name character varying NOT NULL,
  description text,
  importance character varying CHECK (importance::text = ANY (ARRAY['high'::character varying, 'medium'::character varying, 'low'::character varying]::text[])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT credit_card_comparison_features_pkey PRIMARY KEY (id),
  CONSTRAINT credit_card_comparison_features_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.credit_card_features (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  credit_card_id uuid NOT NULL,
  feature_id character varying NOT NULL,
  value text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT credit_card_features_pkey PRIMARY KEY (id),
  CONSTRAINT credit_card_features_credit_card_id_fkey FOREIGN KEY (credit_card_id) REFERENCES public.credit_cards(id)
);
CREATE TABLE public.credit_card_rewards (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  credit_card_id uuid NOT NULL,
  reward_type character varying NOT NULL CHECK (reward_type::text = ANY (ARRAY['cashback'::character varying, 'points'::character varying, 'miles'::character varying, 'other'::character varying]::text[])),
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  description text NOT NULL,
  date date NOT NULL,
  category character varying,
  is_redeemed boolean DEFAULT false,
  redeemed_date date,
  redeemed_value numeric,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT credit_card_rewards_pkey PRIMARY KEY (id),
  CONSTRAINT credit_card_rewards_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT credit_card_rewards_credit_card_id_fkey FOREIGN KEY (credit_card_id) REFERENCES public.credit_cards(id)
);
CREATE TABLE public.credit_card_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  credit_card_id uuid,
  transaction_id uuid,
  statement_month date NOT NULL,
  is_paid boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT credit_card_transactions_pkey PRIMARY KEY (id),
  CONSTRAINT credit_card_transactions_credit_card_id_fkey FOREIGN KEY (credit_card_id) REFERENCES public.credit_cards(id),
  CONSTRAINT credit_card_transactions_transaction_id_fkey FOREIGN KEY (transaction_id) REFERENCES public.transactions(id)
);
CREATE TABLE public.credit_cards (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  card_name text NOT NULL,
  card_number_last4 text NOT NULL,
  card_type text NOT NULL,
  bank_name text NOT NULL,
  credit_limit numeric NOT NULL CHECK (credit_limit > 0::numeric),
  available_credit numeric NOT NULL CHECK (available_credit >= 0::numeric),
  statement_date integer NOT NULL CHECK (statement_date >= 1 AND statement_date <= 31),
  due_date integer NOT NULL CHECK (due_date >= 1 AND due_date <= 31),
  interest_rate numeric NOT NULL CHECK (interest_rate >= 0::numeric),
  annual_fee numeric DEFAULT 0,
  rewards_program text,
  expiry_date date NOT NULL,
  status text NOT NULL CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'expired'::text])),
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  search_vector tsvector,
  current_balance numeric NOT NULL DEFAULT 0 CHECK (current_balance >= 0::numeric),
  rewards_rate numeric,
  foreign_transaction_fee numeric,
  late_payment_fee numeric,
  min_payment_percentage numeric,
  grace_period integer,
  benefits jsonb,
  CONSTRAINT credit_cards_pkey PRIMARY KEY (id),
  CONSTRAINT credit_cards_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.credit_scores (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  score integer NOT NULL CHECK (score >= 300 AND score <= 900),
  source character varying NOT NULL,
  date date NOT NULL,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT credit_scores_pkey PRIMARY KEY (id),
  CONSTRAINT credit_scores_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.currency_management (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  currency_code character varying NOT NULL UNIQUE,
  currency_name character varying NOT NULL,
  currency_symbol character varying,
  decimal_places integer DEFAULT 2,
  is_active boolean DEFAULT true,
  is_base_currency boolean DEFAULT false,
  exchange_rate numeric,
  rate_source character varying,
  last_rate_update timestamp with time zone,
  rate_update_frequency integer DEFAULT 3600,
  auto_update_enabled boolean DEFAULT true,
  historical_rates jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT currency_management_pkey PRIMARY KEY (id)
);
CREATE TABLE public.data_integrity_checks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  check_name character varying NOT NULL,
  check_type character varying NOT NULL,
  target_table character varying,
  target_id uuid,
  status character varying DEFAULT 'pending'::character varying,
  severity character varying DEFAULT 'medium'::character varying,
  findings jsonb,
  recommendations jsonb,
  scheduled_by uuid,
  next_scheduled_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT data_integrity_checks_pkey PRIMARY KEY (id),
  CONSTRAINT data_integrity_checks_scheduled_by_fkey FOREIGN KEY (scheduled_by) REFERENCES auth.users(id)
);
CREATE TABLE public.data_privacy_requests (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  request_type character varying NOT NULL,
  user_id uuid,
  requester_email character varying NOT NULL,
  requester_name character varying,
  legal_basis character varying,
  request_details text NOT NULL,
  verification_method character varying,
  verification_status character varying DEFAULT 'pending'::character varying,
  status character varying DEFAULT 'received'::character varying,
  priority character varying DEFAULT 'normal'::character varying,
  due_date date,
  assigned_to uuid,
  processing_notes text,
  data_categories jsonb,
  data_sources jsonb,
  third_parties_notified jsonb,
  response_method character varying,
  response_sent_at timestamp with time zone,
  completion_notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT data_privacy_requests_pkey PRIMARY KEY (id),
  CONSTRAINT data_privacy_requests_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES auth.users(id),
  CONSTRAINT data_privacy_requests_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.email_templates (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  subject text NOT NULL,
  html_content text NOT NULL,
  text_content text NOT NULL,
  variables ARRAY DEFAULT '{}'::text[],
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT email_templates_pkey PRIMARY KEY (id)
);
CREATE TABLE public.error_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  error_type character varying NOT NULL,
  error_level character varying NOT NULL,
  error_code character varying,
  error_message text NOT NULL,
  stack_trace text,
  user_id uuid,
  session_id character varying,
  page_url text,
  user_agent text,
  ip_address inet,
  component character varying,
  function_name character varying,
  line_number integer,
  resolved boolean DEFAULT false,
  resolved_at timestamp with time zone,
  resolved_by uuid,
  resolution_notes text,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT error_logs_pkey PRIMARY KEY (id),
  CONSTRAINT error_logs_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES auth.users(id),
  CONSTRAINT error_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.export_history (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  name text NOT NULL,
  description text,
  format text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  status text NOT NULL,
  file_url text,
  file_size integer,
  error text,
  metadata jsonb,
  schedule_id uuid,
  CONSTRAINT export_history_pkey PRIMARY KEY (id),
  CONSTRAINT export_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT export_history_schedule_id_fkey FOREIGN KEY (schedule_id) REFERENCES public.export_schedules(id)
);
CREATE TABLE public.export_schedules (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  description text,
  template_id uuid NOT NULL,
  recurrence text NOT NULL,
  day_of_week integer,
  day_of_month integer,
  month_of_year integer,
  time text NOT NULL,
  next_run_date timestamp with time zone NOT NULL,
  last_run_date timestamp with time zone,
  is_active boolean NOT NULL DEFAULT true,
  delivery_method text NOT NULL,
  delivery_config jsonb NOT NULL,
  user_id uuid NOT NULL,
  filters jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT export_schedules_pkey PRIMARY KEY (id),
  CONSTRAINT export_schedules_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.export_templates (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  description text,
  fields jsonb NOT NULL,
  format text NOT NULL,
  user_id uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT export_templates_pkey PRIMARY KEY (id),
  CONSTRAINT export_templates_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.family_group_members (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  group_id text NOT NULL,
  user_id uuid NOT NULL,
  role text NOT NULL DEFAULT 'member'::text CHECK (role = ANY (ARRAY['admin'::text, 'member'::text])),
  invited_by uuid,
  joined_at timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  CONSTRAINT family_group_members_pkey PRIMARY KEY (id),
  CONSTRAINT family_group_members_invited_by_fkey FOREIGN KEY (invited_by) REFERENCES auth.users(id),
  CONSTRAINT family_group_members_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.family_groups(id),
  CONSTRAINT family_group_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.family_groups (
  id text NOT NULL,
  name text NOT NULL,
  creator_id uuid NOT NULL,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  CONSTRAINT family_groups_pkey PRIMARY KEY (id),
  CONSTRAINT family_groups_creator_id_fkey FOREIGN KEY (creator_id) REFERENCES auth.users(id)
);
CREATE TABLE public.feature_audit_log (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  feature_key character varying NOT NULL,
  action character varying NOT NULL,
  old_value jsonb,
  new_value jsonb,
  user_id uuid,
  admin_id uuid,
  reason text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT feature_audit_log_pkey PRIMARY KEY (id),
  CONSTRAINT feature_audit_log_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES auth.users(id)
);
CREATE TABLE public.feature_flags (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  feature_key character varying NOT NULL UNIQUE,
  feature_name character varying NOT NULL,
  description text,
  category character varying NOT NULL,
  is_enabled boolean NOT NULL DEFAULT true,
  is_beta boolean NOT NULL DEFAULT false,
  requires_subscription boolean NOT NULL DEFAULT false,
  min_user_role character varying DEFAULT 'user'::character varying,
  rollout_percentage integer DEFAULT 100 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
  dependencies ARRAY,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  usage_count integer DEFAULT 0,
  active_users integer DEFAULT 0,
  is_premium boolean DEFAULT false,
  CONSTRAINT feature_flags_pkey PRIMARY KEY (id),
  CONSTRAINT feature_flags_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT feature_flags_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.feature_usage (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  feature_name text NOT NULL,
  usage_count integer DEFAULT 1,
  last_used timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT feature_usage_pkey PRIMARY KEY (id),
  CONSTRAINT feature_usage_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.feature_usage_stats (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  feature_key character varying NOT NULL,
  user_id uuid,
  usage_type character varying NOT NULL,
  session_id character varying,
  duration_ms integer,
  success boolean DEFAULT true,
  error_message text,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT feature_usage_stats_pkey PRIMARY KEY (id),
  CONSTRAINT feature_usage_stats_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.feature_user_overrides (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  feature_key character varying NOT NULL,
  is_enabled boolean NOT NULL,
  reason text,
  expires_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  CONSTRAINT feature_user_overrides_pkey PRIMARY KEY (id),
  CONSTRAINT feature_user_overrides_feature_key_fkey FOREIGN KEY (feature_key) REFERENCES public.feature_flags(feature_key),
  CONSTRAINT feature_user_overrides_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT feature_user_overrides_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.financial_goals (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  goal_name character varying NOT NULL,
  goal_type character varying NOT NULL,
  target_amount numeric NOT NULL,
  current_amount numeric DEFAULT 0,
  target_date date,
  priority integer DEFAULT 1,
  status character varying DEFAULT 'active'::character varying,
  description text,
  category character varying,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT financial_goals_pkey PRIMARY KEY (id),
  CONSTRAINT financial_goals_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.gcs_configuration (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id character varying NOT NULL,
  bucket_name character varying NOT NULL,
  service_account_key text NOT NULL CHECK (length(service_account_key) > 0),
  is_active boolean DEFAULT true,
  is_primary boolean DEFAULT true,
  configured_by_admin_id uuid,
  admin_notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  last_tested_at timestamp with time zone,
  CONSTRAINT gcs_configuration_pkey PRIMARY KEY (id),
  CONSTRAINT gcs_configuration_configured_by_admin_id_fkey FOREIGN KEY (configured_by_admin_id) REFERENCES auth.users(id)
);
CREATE TABLE public.gcs_security_events (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  config_id uuid,
  event_type character varying NOT NULL,
  event_description text NOT NULL,
  severity character varying DEFAULT 'info'::character varying CHECK (severity::text = ANY (ARRAY['info'::character varying, 'warning'::character varying, 'error'::character varying, 'critical'::character varying]::text[])),
  user_id uuid,
  ip_address inet,
  user_agent text,
  event_data jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT gcs_security_events_pkey PRIMARY KEY (id),
  CONSTRAINT gcs_security_events_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT gcs_security_events_config_id_fkey FOREIGN KEY (config_id) REFERENCES public.gcs_configuration(id)
);
CREATE TABLE public.gcs_uploads (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  config_id uuid,
  gcs_object_name character varying NOT NULL UNIQUE,
  original_file_name character varying NOT NULL,
  file_size bigint NOT NULL CHECK (file_size > 0),
  mime_type character varying NOT NULL,
  public_url text NOT NULL,
  transaction_id uuid,
  transaction_type character varying NOT NULL CHECK (transaction_type::text = ANY (ARRAY['income'::character varying, 'expense'::character varying, 'loan'::character varying, 'investment'::character varying]::text[])),
  folder_path text,
  upload_source character varying DEFAULT 'web'::character varying CHECK (upload_source::text = ANY (ARRAY['web'::character varying, 'telegram'::character varying, 'api'::character varying]::text[])),
  upload_status character varying DEFAULT 'completed'::character varying CHECK (upload_status::text = ANY (ARRAY['pending'::character varying, 'completed'::character varying, 'failed'::character varying]::text[])),
  upload_error text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT gcs_uploads_pkey PRIMARY KEY (id),
  CONSTRAINT gcs_uploads_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT gcs_uploads_config_id_fkey FOREIGN KEY (config_id) REFERENCES public.gcs_configuration(id)
);
CREATE TABLE public.gcs_usage_stats (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  config_id uuid NOT NULL UNIQUE,
  total_uploads integer DEFAULT 0,
  total_bytes_uploaded bigint DEFAULT 0,
  successful_uploads integer DEFAULT 0,
  failed_uploads integer DEFAULT 0,
  average_upload_time_ms integer DEFAULT 0,
  last_successful_upload timestamp with time zone,
  last_failed_upload timestamp with time zone,
  estimated_storage_used bigint DEFAULT 0,
  last_usage_check timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT gcs_usage_stats_pkey PRIMARY KEY (id),
  CONSTRAINT gcs_usage_stats_config_id_fkey FOREIGN KEY (config_id) REFERENCES public.gcs_configuration(id)
);
CREATE TABLE public.households (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  owner_id uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT households_pkey PRIMARY KEY (id),
  CONSTRAINT households_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES auth.users(id)
);
CREATE TABLE public.investments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  name text NOT NULL,
  type text NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  purchase_date timestamp with time zone NOT NULL,
  current_value numeric NOT NULL CHECK (current_value >= 0::numeric),
  last_updated timestamp with time zone NOT NULL DEFAULT now(),
  notes text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  search_vector tsvector,
  CONSTRAINT investments_pkey PRIMARY KEY (id),
  CONSTRAINT investments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.loan_payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  loan_id uuid,
  payment_date timestamp with time zone NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  principal_component numeric NOT NULL,
  interest_component numeric NOT NULL,
  extra_payment numeric DEFAULT 0,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT loan_payments_pkey PRIMARY KEY (id),
  CONSTRAINT loan_payments_loan_id_fkey FOREIGN KEY (loan_id) REFERENCES public.loans(id)
);
CREATE TABLE public.loans (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  name text NOT NULL,
  type text NOT NULL,
  lender text NOT NULL,
  principal_amount numeric NOT NULL CHECK (principal_amount > 0::numeric),
  interest_rate numeric NOT NULL CHECK (interest_rate >= 0::numeric),
  interest_type text NOT NULL CHECK (interest_type = ANY (ARRAY['fixed'::text, 'floating'::text])),
  loan_term_months integer NOT NULL CHECK (loan_term_months > 0),
  start_date timestamp with time zone NOT NULL,
  end_date timestamp with time zone NOT NULL,
  payment_amount numeric NOT NULL CHECK (payment_amount > 0::numeric),
  payment_frequency text NOT NULL CHECK (payment_frequency = ANY (ARRAY['monthly'::text, 'quarterly'::text, 'semi-annually'::text, 'annually'::text])),
  remaining_amount numeric NOT NULL CHECK (remaining_amount >= 0::numeric),
  prepayment_penalty numeric DEFAULT 0,
  notes text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  search_vector tsvector,
  CONSTRAINT loans_pkey PRIMARY KEY (id),
  CONSTRAINT loans_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.ml_models (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['timing_optimization'::character varying, 'content_optimization'::character varying, 'delivery_method'::character varying, 'frequency_capping'::character varying]::text[])),
  version character varying NOT NULL,
  status character varying NOT NULL DEFAULT 'training'::character varying CHECK (status::text = ANY (ARRAY['training'::character varying, 'active'::character varying, 'deprecated'::character varying]::text[])),
  accuracy numeric,
  training_data_size integer,
  last_trained timestamp with time zone,
  features ARRAY DEFAULT '{}'::text[],
  hyperparameters jsonb DEFAULT '{}'::jsonb,
  model_data jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ml_models_pkey PRIMARY KEY (id)
);
CREATE TABLE public.ml_predictions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  model_id uuid NOT NULL,
  user_id uuid NOT NULL,
  notification_id uuid,
  prediction_type character varying NOT NULL,
  input_features jsonb NOT NULL,
  prediction jsonb NOT NULL,
  confidence numeric NOT NULL,
  actual_outcome jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ml_predictions_pkey PRIMARY KEY (id),
  CONSTRAINT ml_predictions_model_id_fkey FOREIGN KEY (model_id) REFERENCES public.ml_models(id),
  CONSTRAINT ml_predictions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.nlp_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  original_text_hash text NOT NULL,
  language text DEFAULT 'en'::text,
  intent_confidence numeric,
  entity_confidence numeric,
  overall_confidence numeric,
  entities_found integer DEFAULT 0,
  fallback_used boolean DEFAULT false,
  processing_time_ms integer,
  intent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT nlp_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT nlp_metrics_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.notification_analytics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  notification_id uuid,
  event_type character varying NOT NULL,
  delivery_method character varying,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT notification_analytics_pkey PRIMARY KEY (id),
  CONSTRAINT notification_analytics_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.notification_channels (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  type text NOT NULL CHECK (type = ANY (ARRAY['email'::text, 'sms'::text, 'push'::text, 'slack'::text, 'webhook'::text])),
  config jsonb NOT NULL DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT notification_channels_pkey PRIMARY KEY (id)
);
CREATE TABLE public.notification_deliveries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  announcement_id uuid,
  channel_id uuid,
  channel_type text NOT NULL,
  recipient text NOT NULL,
  status text DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'sent'::text, 'delivered'::text, 'failed'::text, 'bounced'::text])),
  sent_at timestamp with time zone,
  delivered_at timestamp with time zone,
  error_message text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT notification_deliveries_pkey PRIMARY KEY (id),
  CONSTRAINT notification_deliveries_announcement_id_fkey FOREIGN KEY (announcement_id) REFERENCES public.announcements(id),
  CONSTRAINT notification_deliveries_channel_id_fkey FOREIGN KEY (channel_id) REFERENCES public.notification_channels(id)
);
CREATE TABLE public.notification_preferences (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  transaction_alerts boolean DEFAULT true,
  budget_warnings boolean DEFAULT true,
  payment_reminders boolean DEFAULT true,
  system_messages boolean DEFAULT true,
  email_enabled boolean DEFAULT false,
  push_enabled boolean DEFAULT true,
  quiet_hours_start time without time zone,
  quiet_hours_end time without time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT notification_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT notification_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  type USER-DEFINED NOT NULL,
  priority USER-DEFINED NOT NULL DEFAULT 'medium'::notification_priority,
  title text NOT NULL CHECK (length(TRIM(BOTH FROM title)) > 0),
  message text NOT NULL CHECK (length(TRIM(BOTH FROM message)) > 0),
  action_url text,
  action_text text,
  is_read boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT notifications_pkey PRIMARY KEY (id),
  CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.ocr_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  image_size integer,
  image_format text,
  image_quality numeric,
  processing_time_ms integer,
  confidence numeric,
  providers_used ARRAY,
  text_length integer,
  items_extracted integer DEFAULT 0,
  layout_analysis_used boolean DEFAULT false,
  merchant_detected boolean DEFAULT false,
  amount_detected boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ocr_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT ocr_metrics_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.payment_methods (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  type character varying NOT NULL,
  icon character varying,
  color character varying,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  user_id uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT payment_methods_pkey PRIMARY KEY (id),
  CONSTRAINT payment_methods_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  display_name character varying NOT NULL,
  description text,
  category character varying NOT NULL,
  resource character varying NOT NULL,
  action character varying NOT NULL,
  is_system_permission boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT permissions_pkey PRIMARY KEY (id)
);
CREATE TABLE public.predictive_alerts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  alert_type text NOT NULL,
  risk_level numeric NOT NULL,
  confidence numeric NOT NULL,
  prediction_data jsonb,
  acknowledged boolean DEFAULT false,
  acknowledged_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  severity text CHECK (severity = ANY (ARRAY['low'::text, 'medium'::text, 'high'::text, 'critical'::text])),
  recommendation text,
  next_action text,
  expires_at timestamp with time zone,
  CONSTRAINT predictive_alerts_pkey PRIMARY KEY (id),
  CONSTRAINT predictive_alerts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.push_subscriptions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  endpoint text NOT NULL,
  p256dh_key text NOT NULL,
  auth_key text NOT NULL,
  user_agent text,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT push_subscriptions_pkey PRIMARY KEY (id),
  CONSTRAINT push_subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.recurring_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  transaction_template jsonb NOT NULL,
  frequency text NOT NULL CHECK (frequency = ANY (ARRAY['daily'::text, 'weekly'::text, 'monthly'::text, 'yearly'::text])),
  start_date timestamp with time zone NOT NULL,
  end_date timestamp with time zone,
  last_generated timestamp with time zone NOT NULL DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_paused boolean NOT NULL DEFAULT false,
  CONSTRAINT recurring_transactions_pkey PRIMARY KEY (id),
  CONSTRAINT recurring_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.report_definitions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  type character varying NOT NULL,
  category character varying NOT NULL,
  parameters jsonb,
  query_template text NOT NULL,
  chart_config jsonb,
  is_system_report boolean NOT NULL DEFAULT false,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT report_definitions_pkey PRIMARY KEY (id),
  CONSTRAINT report_definitions_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.report_executions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  report_id uuid NOT NULL,
  parameters jsonb,
  status character varying NOT NULL DEFAULT 'pending'::character varying,
  data jsonb,
  error_message text,
  executed_by uuid NOT NULL,
  executed_at timestamp with time zone DEFAULT now(),
  completed_at timestamp with time zone,
  CONSTRAINT report_executions_pkey PRIMARY KEY (id),
  CONSTRAINT report_executions_report_id_fkey FOREIGN KEY (report_id) REFERENCES public.report_definitions(id),
  CONSTRAINT report_executions_executed_by_fkey FOREIGN KEY (executed_by) REFERENCES auth.users(id)
);
CREATE TABLE public.role_permissions (
  role_id uuid NOT NULL,
  permission_id uuid NOT NULL,
  granted_by uuid NOT NULL,
  granted_at timestamp with time zone DEFAULT now(),
  CONSTRAINT role_permissions_pkey PRIMARY KEY (role_id, permission_id),
  CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id),
  CONSTRAINT role_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES auth.users(id),
  CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id)
);
CREATE TABLE public.roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  display_name character varying NOT NULL,
  description text,
  level integer NOT NULL DEFAULT 0,
  parent_role_id uuid,
  is_system_role boolean NOT NULL DEFAULT false,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT roles_pkey PRIMARY KEY (id),
  CONSTRAINT roles_parent_role_id_fkey FOREIGN KEY (parent_role_id) REFERENCES public.roles(id)
);
CREATE TABLE public.scheduled_notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  title character varying NOT NULL,
  message text NOT NULL,
  scheduled_for timestamp with time zone NOT NULL,
  recurring_pattern character varying,
  recurring_until timestamp with time zone,
  status character varying NOT NULL DEFAULT 'scheduled'::character varying CHECK (status::text = ANY (ARRAY['scheduled'::character varying, 'sent'::character varying, 'cancelled'::character varying, 'failed'::character varying]::text[])),
  delivery_methods ARRAY DEFAULT '{push}'::text[],
  action_url text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT scheduled_notifications_pkey PRIMARY KEY (id),
  CONSTRAINT scheduled_notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.schema_migrations (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  version text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT schema_migrations_pkey PRIMARY KEY (id)
);
CREATE TABLE public.security_incidents (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  incident_type character varying NOT NULL,
  severity character varying NOT NULL,
  title character varying NOT NULL,
  description text NOT NULL,
  affected_systems ARRAY,
  affected_users ARRAY,
  attack_vector character varying,
  source_ip inet,
  user_agent text,
  detection_method character varying,
  status character varying DEFAULT 'open'::character varying,
  priority character varying DEFAULT 'medium'::character varying,
  assigned_to uuid,
  reported_by uuid,
  detected_at timestamp with time zone DEFAULT now(),
  acknowledged_at timestamp with time zone,
  contained_at timestamp with time zone,
  resolved_at timestamp with time zone,
  closed_at timestamp with time zone,
  response_actions jsonb,
  lessons_learned text,
  cost_estimate numeric,
  compliance_impact jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT security_incidents_pkey PRIMARY KEY (id),
  CONSTRAINT security_incidents_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES auth.users(id),
  CONSTRAINT security_incidents_reported_by_fkey FOREIGN KEY (reported_by) REFERENCES auth.users(id)
);
CREATE TABLE public.shared_budgets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  group_id text NOT NULL,
  name text NOT NULL,
  total_amount numeric NOT NULL,
  spent_amount numeric DEFAULT 0,
  category text,
  period_start date NOT NULL,
  period_end date NOT NULL,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  CONSTRAINT shared_budgets_pkey PRIMARY KEY (id),
  CONSTRAINT shared_budgets_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.family_groups(id),
  CONSTRAINT shared_budgets_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.smart_categories_preferences (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  enabled boolean NOT NULL DEFAULT true,
  auto_categorize boolean NOT NULL DEFAULT false,
  show_suggestions boolean NOT NULL DEFAULT true,
  confidence_threshold numeric NOT NULL DEFAULT 0.8 CHECK (confidence_threshold >= 0.5 AND confidence_threshold <= 1.0),
  auto_apply_high_confidence boolean NOT NULL DEFAULT false,
  learn_from_manual_changes boolean NOT NULL DEFAULT true,
  suggest_tags boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT smart_categories_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT smart_categories_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.smart_categorization_history (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  transaction_description text NOT NULL,
  suggested_category character varying,
  actual_category character varying NOT NULL,
  confidence_score numeric,
  was_suggestion_accepted boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT smart_categorization_history_pkey PRIMARY KEY (id),
  CONSTRAINT smart_categorization_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.smart_notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  category character varying NOT NULL,
  priority character varying DEFAULT 'medium'::character varying CHECK (priority::text = ANY (ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'urgent'::character varying]::text[])),
  trigger_type character varying NOT NULL,
  title character varying NOT NULL,
  message text NOT NULL,
  action_text character varying,
  action_url character varying,
  trigger jsonb DEFAULT '{}'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  is_read boolean DEFAULT false,
  is_dismissed boolean DEFAULT false,
  scheduled_for timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  read_at timestamp with time zone,
  dismissed_at timestamp with time zone,
  CONSTRAINT smart_notifications_pkey PRIMARY KEY (id),
  CONSTRAINT smart_notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.smart_schedules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  reminder_type text NOT NULL,
  schedule_data jsonb NOT NULL,
  is_active boolean DEFAULT true,
  effectiveness_score numeric,
  last_optimization timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  optimization_count integer DEFAULT 0,
  CONSTRAINT smart_schedules_pkey PRIMARY KEY (id),
  CONSTRAINT smart_schedules_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.system_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  metric_type character varying NOT NULL,
  metric_name character varying NOT NULL,
  metric_value numeric NOT NULL,
  unit character varying,
  threshold_warning numeric,
  threshold_critical numeric,
  status character varying DEFAULT 'normal'::character varying,
  metadata jsonb,
  recorded_at timestamp with time zone DEFAULT now(),
  CONSTRAINT system_metrics_pkey PRIMARY KEY (id)
);
CREATE TABLE public.system_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  setting_key text NOT NULL UNIQUE,
  setting_value jsonb NOT NULL,
  description text,
  category text DEFAULT 'general'::text,
  is_public boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  updated_by uuid,
  CONSTRAINT system_settings_pkey PRIMARY KEY (id),
  CONSTRAINT system_settings_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.telegram_attachments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  telegram_user_id uuid,
  transaction_id uuid,
  file_id text NOT NULL,
  file_name text,
  file_type text NOT NULL,
  file_size integer,
  mime_type text,
  storage_url text NOT NULL,
  storage_path text NOT NULL,
  upload_status text DEFAULT 'pending'::text CHECK (upload_status = ANY (ARRAY['pending'::text, 'uploading'::text, 'completed'::text, 'failed'::text])),
  metadata jsonb,
  uploaded_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_attachments_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_attachments_transaction_id_fkey FOREIGN KEY (transaction_id) REFERENCES public.transactions(id),
  CONSTRAINT telegram_attachments_telegram_user_id_fkey FOREIGN KEY (telegram_user_id) REFERENCES public.telegram_users(id),
  CONSTRAINT telegram_attachments_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.telegram_auth_codes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  code text NOT NULL UNIQUE,
  user_id uuid NOT NULL,
  expires_at timestamp with time zone NOT NULL,
  used_at timestamp with time zone,
  is_used boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_auth_codes_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_auth_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.telegram_exports (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  telegram_user_id uuid,
  export_type text NOT NULL,
  export_format text DEFAULT 'text'::text CHECK (export_format = ANY (ARRAY['text'::text, 'csv'::text, 'json'::text, 'pdf'::text])),
  filters jsonb,
  date_range jsonb,
  status text DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'processing'::text, 'completed'::text, 'failed'::text])),
  file_url text,
  file_size integer,
  record_count integer,
  error_message text,
  expires_at timestamp with time zone,
  requested_at timestamp with time zone DEFAULT now(),
  completed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_exports_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_exports_telegram_user_id_fkey FOREIGN KEY (telegram_user_id) REFERENCES public.telegram_users(id),
  CONSTRAINT telegram_exports_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.telegram_insights_cache (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  cache_key text NOT NULL,
  insights_data jsonb NOT NULL,
  insights_text text NOT NULL,
  confidence_score numeric,
  data_points_analyzed integer,
  analysis_period text,
  expires_at timestamp with time zone NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_insights_cache_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_insights_cache_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.telegram_interactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  telegram_user_id text NOT NULL,
  command text,
  description text,
  success boolean DEFAULT false,
  error_message text,
  timestamp timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_interactions_pkey PRIMARY KEY (id)
);
CREATE TABLE public.telegram_notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  telegram_user_id uuid,
  notification_type text NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  scheduled_for timestamp with time zone,
  sent_at timestamp with time zone,
  status text DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'sent'::text, 'failed'::text, 'cancelled'::text])),
  priority text DEFAULT 'normal'::text CHECK (priority = ANY (ARRAY['low'::text, 'normal'::text, 'high'::text, 'urgent'::text])),
  metadata jsonb,
  retry_count integer DEFAULT 0,
  max_retries integer DEFAULT 3,
  error_message text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_notifications_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT telegram_notifications_telegram_user_id_fkey FOREIGN KEY (telegram_user_id) REFERENCES public.telegram_users(id)
);
CREATE TABLE public.telegram_sessions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  telegram_user_id uuid NOT NULL,
  session_token text NOT NULL UNIQUE,
  expires_at timestamp with time zone NOT NULL,
  is_active boolean DEFAULT true,
  last_activity timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_sessions_telegram_user_id_fkey FOREIGN KEY (telegram_user_id) REFERENCES public.telegram_users(id)
);
CREATE TABLE public.telegram_users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  telegram_id bigint NOT NULL UNIQUE,
  user_id uuid NOT NULL,
  username text,
  first_name text,
  last_name text,
  language_code text DEFAULT 'en'::text,
  linked_at timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  preferences jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT telegram_users_pkey PRIMARY KEY (id),
  CONSTRAINT telegram_users_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.threat_detection (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  threat_type character varying NOT NULL,
  threat_level character varying NOT NULL,
  confidence_score numeric,
  threat_description text NOT NULL,
  affected_entity_type character varying,
  affected_entity_id character varying,
  source_ip inet,
  status character varying DEFAULT 'active'::character varying,
  detected_at timestamp with time zone DEFAULT now(),
  resolved_at timestamp with time zone,
  conducted_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT threat_detection_pkey PRIMARY KEY (id),
  CONSTRAINT threat_detection_conducted_by_fkey FOREIGN KEY (conducted_by) REFERENCES auth.users(id)
);
CREATE TABLE public.transaction_monitoring (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  transaction_id uuid NOT NULL,
  user_id uuid NOT NULL,
  monitoring_type character varying NOT NULL,
  risk_level character varying NOT NULL,
  risk_score numeric,
  alert_reason text NOT NULL,
  detection_rules jsonb,
  transaction_data jsonb,
  status character varying DEFAULT 'pending'::character varying,
  investigated_by uuid,
  investigated_at timestamp with time zone,
  resolution_notes text,
  actions_taken jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT transaction_monitoring_pkey PRIMARY KEY (id),
  CONSTRAINT transaction_monitoring_investigated_by_fkey FOREIGN KEY (investigated_by) REFERENCES auth.users(id),
  CONSTRAINT transaction_monitoring_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  type text NOT NULL CHECK (type = ANY (ARRAY['income'::text, 'expense'::text])),
  category text NOT NULL,
  source text NOT NULL,
  date timestamp with time zone NOT NULL DEFAULT now(),
  description text,
  attachments ARRAY DEFAULT ARRAY[]::text[],
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_recurring boolean DEFAULT false,
  recurring_frequency text CHECK (recurring_frequency = ANY (ARRAY['daily'::text, 'weekly'::text, 'monthly'::text, 'yearly'::text])),
  recurring_end_date date,
  next_occurrence date,
  parent_transaction_id uuid,
  metadata jsonb DEFAULT '{}'::jsonb,
  search_vector tsvector,
  source_type text DEFAULT 'manual'::text,
  source_metadata jsonb DEFAULT '{}'::jsonb,
  confidence_score numeric,
  family_group_id text,
  approval_request_id text,
  payment_method character varying,
  location text,
  location_coordinates point,
  attachment_url text,
  nlp_parsed boolean DEFAULT false,
  original_message text,
  CONSTRAINT transactions_pkey PRIMARY KEY (id),
  CONSTRAINT transactions_approval_request_id_fkey FOREIGN KEY (approval_request_id) REFERENCES public.approval_requests(id),
  CONSTRAINT transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT transactions_parent_transaction_id_fkey FOREIGN KEY (parent_transaction_id) REFERENCES public.transactions(id),
  CONSTRAINT transactions_family_group_id_fkey FOREIGN KEY (family_group_id) REFERENCES public.family_groups(id)
);
CREATE TABLE public.user_2fa_attempts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  method character varying NOT NULL,
  ip_address inet,
  user_agent text,
  success boolean NOT NULL DEFAULT false,
  attempted_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_2fa_attempts_pkey PRIMARY KEY (id),
  CONSTRAINT user_2fa_attempts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_2fa_sessions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  session_id text NOT NULL,
  method_used character varying NOT NULL,
  verified_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  ip_address inet,
  user_agent text,
  CONSTRAINT user_2fa_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT user_2fa_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_2fa_setup (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  method character varying NOT NULL CHECK (method::text = ANY (ARRAY['2fa_totp'::character varying, '2fa_sms'::character varying, '2fa_email'::character varying]::text[])),
  secret text,
  phone_number text,
  backup_codes jsonb NOT NULL DEFAULT '[]'::jsonb,
  is_verified boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  last_used_at timestamp with time zone,
  CONSTRAINT user_2fa_setup_pkey PRIMARY KEY (id),
  CONSTRAINT user_2fa_setup_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_activity_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  session_id character varying,
  activity_type character varying NOT NULL,
  activity_name character varying NOT NULL,
  page_url text,
  user_agent text,
  ip_address inet,
  country character varying,
  city character varying,
  device_type character varying,
  browser character varying,
  duration_ms integer,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_activity_logs_pkey PRIMARY KEY (id),
  CONSTRAINT user_activity_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_announcements (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  announcement_id uuid NOT NULL,
  viewed_at timestamp with time zone DEFAULT now(),
  dismissed_at timestamp with time zone,
  clicked boolean DEFAULT false,
  clicked_at timestamp with time zone,
  CONSTRAINT user_announcements_pkey PRIMARY KEY (id),
  CONSTRAINT user_announcements_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_behavior_data (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  behavior_type text NOT NULL,
  data jsonb NOT NULL,
  recorded_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_behavior_data_pkey PRIMARY KEY (id),
  CONSTRAINT user_behavior_data_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_behavior_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  interaction_type text NOT NULL,
  time_slot text,
  language text DEFAULT 'en'::text,
  session_duration_ms integer,
  commands_in_session integer DEFAULT 0,
  errors_in_session integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_behavior_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT user_behavior_metrics_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_behavior_profiles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  activity_patterns jsonb DEFAULT '{}'::jsonb,
  response_rates jsonb DEFAULT '{}'::jsonb,
  engagement_levels jsonb DEFAULT '{}'::jsonb,
  stress_indicators jsonb DEFAULT '{}'::jsonb,
  preferences jsonb DEFAULT '{}'::jsonb,
  confidence numeric DEFAULT 0.3,
  data_points integer DEFAULT 0,
  last_updated timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_behavior_profiles_pkey PRIMARY KEY (id),
  CONSTRAINT user_behavior_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_bulk_operations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  operation_type character varying NOT NULL,
  operation_name character varying NOT NULL,
  description text,
  target_criteria jsonb,
  affected_user_count integer DEFAULT 0,
  status character varying DEFAULT 'pending'::character varying,
  progress_percentage numeric DEFAULT 0.00,
  results jsonb,
  error_details jsonb,
  initiated_by uuid NOT NULL,
  completed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_bulk_operations_pkey PRIMARY KEY (id),
  CONSTRAINT user_bulk_operations_initiated_by_fkey FOREIGN KEY (initiated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.user_conversation_context (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  preferences jsonb DEFAULT '{}'::jsonb,
  patterns jsonb DEFAULT '{}'::jsonb,
  session_stats jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_conversation_context_pkey PRIMARY KEY (id),
  CONSTRAINT user_conversation_context_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_feedback (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  feedback_type text NOT NULL CHECK (feedback_type = ANY (ARRAY['correction'::text, 'confirmation'::text, 'rejection'::text])),
  original_confidence numeric,
  correction_type text,
  response_time_ms integer,
  confidence_range text,
  original_data jsonb,
  corrected_data jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_feedback_pkey PRIMARY KEY (id),
  CONSTRAINT user_feedback_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_interactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  type text NOT NULL,
  action text,
  metadata jsonb,
  response_time integer,
  success boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  error_message text,
  processing_time_ms integer,
  source text DEFAULT 'frontend'::text,
  session_id text,
  CONSTRAINT user_interactions_pkey PRIMARY KEY (id),
  CONSTRAINT user_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_login_history (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  login_type character varying NOT NULL,
  ip_address inet,
  user_agent text,
  device_info jsonb,
  location_info jsonb,
  success boolean NOT NULL,
  failure_reason text,
  session_duration integer,
  created_at timestamp with time zone DEFAULT now(),
  ended_at timestamp with time zone,
  CONSTRAINT user_login_history_pkey PRIMARY KEY (id),
  CONSTRAINT user_login_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_notes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  note_type character varying DEFAULT 'general'::character varying,
  title character varying,
  content text NOT NULL,
  priority character varying DEFAULT 'normal'::character varying,
  is_flagged boolean DEFAULT false,
  created_by uuid NOT NULL,
  updated_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_notes_pkey PRIMARY KEY (id),
  CONSTRAINT user_notes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT user_notes_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT user_notes_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.user_notification_preferences (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  channel_type text NOT NULL,
  is_enabled boolean DEFAULT true,
  preferences jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_notification_preferences_pkey PRIMARY KEY (id)
);
CREATE TABLE public.user_permanent_auth_codes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  auth_code character varying NOT NULL UNIQUE,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  last_used_at timestamp with time zone,
  usage_count integer DEFAULT 0,
  CONSTRAINT user_permanent_auth_codes_pkey PRIMARY KEY (id),
  CONSTRAINT user_permanent_auth_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_personalities (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  personality_type text NOT NULL CHECK (personality_type = ANY (ARRAY['analytical'::text, 'friendly'::text, 'professional'::text, 'creative'::text, 'practical'::text])),
  traits jsonb DEFAULT '[]'::jsonb,
  confidence numeric NOT NULL CHECK (confidence >= 0::numeric AND confidence <= 1::numeric),
  communication_style text DEFAULT 'balanced'::text,
  emotional_indicators jsonb DEFAULT '{}'::jsonb,
  last_updated timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_personalities_pkey PRIMARY KEY (id),
  CONSTRAINT user_personalities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_preferences (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL UNIQUE,
  seen_tutorials jsonb DEFAULT '{}'::jsonb,
  theme character varying DEFAULT 'system'::character varying,
  currency character varying DEFAULT 'USD'::character varying,
  locale character varying DEFAULT 'en-US'::character varying,
  notifications_enabled boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_profiles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  first_name character varying,
  last_name character varying,
  display_name character varying,
  phone character varying,
  date_of_birth date,
  gender character varying,
  address_line1 character varying,
  address_line2 character varying,
  city character varying,
  state character varying,
  postal_code character varying,
  country character varying,
  timezone character varying DEFAULT 'UTC'::character varying,
  language character varying DEFAULT 'en'::character varying,
  currency character varying DEFAULT 'USD'::character varying,
  profile_picture_url text,
  bio text,
  website_url text,
  social_media_links jsonb,
  preferences jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_profiles_pkey PRIMARY KEY (id),
  CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  role_id uuid NOT NULL,
  assigned_by uuid NOT NULL,
  assigned_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone,
  is_active boolean NOT NULL DEFAULT true,
  CONSTRAINT user_roles_pkey PRIMARY KEY (id),
  CONSTRAINT user_roles_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES auth.users(id),
  CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id)
);
CREATE TABLE public.user_segment_memberships (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  segment_id uuid NOT NULL,
  user_id uuid NOT NULL,
  added_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_segment_memberships_pkey PRIMARY KEY (id),
  CONSTRAINT user_segment_memberships_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_segments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  criteria jsonb NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_segments_pkey PRIMARY KEY (id)
);
CREATE TABLE public.user_status_history (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  status character varying NOT NULL,
  previous_status character varying,
  reason text,
  changed_by uuid,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_status_history_pkey PRIMARY KEY (id),
  CONSTRAINT user_status_history_changed_by_fkey FOREIGN KEY (changed_by) REFERENCES auth.users(id),
  CONSTRAINT user_status_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_subscriptions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  plan_type character varying NOT NULL DEFAULT 'free'::character varying CHECK (plan_type::text = ANY (ARRAY['free'::character varying, 'premium'::character varying, 'enterprise'::character varying]::text[])),
  status character varying NOT NULL DEFAULT 'active'::character varying CHECK (status::text = ANY (ARRAY['active'::character varying, 'inactive'::character varying, 'cancelled'::character varying, 'expired'::character varying]::text[])),
  features ARRAY DEFAULT '{}'::text[],
  expires_at timestamp with time zone,
  stripe_subscription_id text,
  stripe_customer_id text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_subscriptions_pkey PRIMARY KEY (id),
  CONSTRAINT user_subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_tags (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  tag_name character varying NOT NULL,
  tag_value character varying,
  tag_category character varying,
  color character varying,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_tags_pkey PRIMARY KEY (id),
  CONSTRAINT user_tags_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT user_tags_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_verification (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  verification_type character varying NOT NULL,
  verification_value character varying,
  is_verified boolean DEFAULT false,
  verified_at timestamp with time zone,
  verification_code character varying,
  expires_at timestamp with time zone,
  attempts integer DEFAULT 0,
  max_attempts integer DEFAULT 3,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_verification_pkey PRIMARY KEY (id),
  CONSTRAINT user_verification_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  email text NOT NULL UNIQUE,
  settings jsonb DEFAULT '{}'::jsonb,
  name text,
  profile_picture_url text,
  phone_number text,
  address text,
  date_of_birth date,
  occupation text,
  preferred_currency text DEFAULT 'INR'::text,
  tax_id text,
  two_factor_enabled boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (id)
);
CREATE TABLE public.voice_commands (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  command text NOT NULL,
  intent text,
  entities jsonb,
  executed_at timestamp with time zone DEFAULT now(),
  success boolean,
  error text,
  confidence numeric CHECK (confidence >= 0::numeric AND confidence <= 1::numeric),
  processing_time_ms integer,
  audio_duration_ms integer,
  language text DEFAULT 'en'::text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT voice_commands_pkey PRIMARY KEY (id),
  CONSTRAINT voice_commands_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.voice_navigation_states (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  current_state text DEFAULT 'main_menu'::text,
  navigation_history jsonb DEFAULT '[]'::jsonb,
  session_id text,
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT voice_navigation_states_pkey PRIMARY KEY (id),
  CONSTRAINT voice_navigation_states_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.vulnerability_assessments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  assessment_name character varying NOT NULL,
  assessment_type character varying NOT NULL,
  target_system character varying NOT NULL,
  status character varying DEFAULT 'scheduled'::character varying,
  total_vulnerabilities integer DEFAULT 0,
  critical_vulnerabilities integer DEFAULT 0,
  high_vulnerabilities integer DEFAULT 0,
  medium_vulnerabilities integer DEFAULT 0,
  low_vulnerabilities integer DEFAULT 0,
  risk_score numeric,
  conducted_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT vulnerability_assessments_pkey PRIMARY KEY (id),
  CONSTRAINT vulnerability_assessments_conducted_by_fkey FOREIGN KEY (conducted_by) REFERENCES auth.users(id)
);
CREATE TABLE public.workplace_integrations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  platform character varying NOT NULL CHECK (platform::text = ANY (ARRAY['slack'::character varying, 'teams'::character varying, 'discord'::character varying]::text[])),
  webhook_url text NOT NULL,
  channel_name character varying,
  bot_token text,
  is_active boolean DEFAULT true,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT workplace_integrations_pkey PRIMARY KEY (id),
  CONSTRAINT workplace_integrations_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.workplace_messages (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  integration_id uuid NOT NULL,
  notification_id uuid NOT NULL,
  platform character varying NOT NULL,
  channel character varying NOT NULL,
  message_content jsonb NOT NULL,
  status character varying NOT NULL DEFAULT 'pending'::character varying CHECK (status::text = ANY (ARRAY['pending'::character varying, 'sent'::character varying, 'failed'::character varying]::text[])),
  platform_message_id character varying,
  error_message text,
  sent_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT workplace_messages_pkey PRIMARY KEY (id),
  CONSTRAINT workplace_messages_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES public.workplace_integrations(id)
);