#!/usr/bin/env node

/**
 * 🧹 Cleanup Script - Remove Old Bot Files
 * 
 * This script removes all old bot instances and keeps only the
 * consolidated finmanager-bot.js with ALL features included.
 */

const fs = require('fs');
const path = require('path');

console.log('🧹 Starting cleanup of old bot files...');

// List of old bot files to remove
const oldBotFiles = [
  'enterprise-bot.js',
  'enhanced-bot.js', 
  'optimized-bot.js',
  'index.js',
  'enhanced-bot-helpers.js', // Will be integrated into main bot
  'test-enhanced-bot.js',
  'test-enhanced-services.js',
  'test-fast-bot.js',
  'test-gcs-integration.js',
  'test-ocr.js',
  'test-sync.js',
  'test-bot.js',
  'simple-test.js',
  'restart-bot.js',
  'restart-fast-bot.js',
  'check-deps.js'
];

// List of old documentation files to remove (keeping only essential ones)
const oldDocFiles = [
  'ECOMMERCE_OCR_GUIDE.md',
  'ECOMMERCE_OCR_SUMMARY.md', 
  'FAST_BOT_DEPLOYMENT.md',
  'OCR_DEPLOYMENT_GUIDE.md',
  'OCR_REMOVAL_SUMMARY.md',
  'OCR_STATUS_REPORT.md'
];

// Files to keep
const keepFiles = [
  'finmanager-bot.js', // THE ONLY BOT FILE WE NEED
  'package.json',
  'package-lock.json',
  '.env',
  'README.md',
  'ENHANCED_BOT_IMPLEMENTATION_GUIDE.md',
  'BACKEND_TABLES_CREATED.md',
  'DEPLOYMENT_READY_SUMMARY.md',
  'start-bot.bat'
];

let removedCount = 0;
let keptCount = 0;

// Remove old bot files
console.log('\n📁 Removing old bot files...');
oldBotFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✅ Removed: ${file}`);
      removedCount++;
    } catch (error) {
      console.log(`❌ Failed to remove: ${file} - ${error.message}`);
    }
  } else {
    console.log(`⚪ Not found: ${file}`);
  }
});

// Remove old documentation files
console.log('\n📄 Removing old documentation files...');
oldDocFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✅ Removed: ${file}`);
      removedCount++;
    } catch (error) {
      console.log(`❌ Failed to remove: ${file} - ${error.message}`);
    }
  } else {
    console.log(`⚪ Not found: ${file}`);
  }
});

// Remove OCR training data (no longer needed)
const ocrFiles = ['eng.traineddata'];
console.log('\n🔍 Removing OCR files...');
ocrFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✅ Removed: ${file}`);
      removedCount++;
    } catch (error) {
      console.log(`❌ Failed to remove: ${file} - ${error.message}`);
    }
  }
});

// Clean up logs directory
const logsDir = path.join(__dirname, 'logs');
if (fs.existsSync(logsDir)) {
  try {
    const logFiles = fs.readdirSync(logsDir);
    logFiles.forEach(file => {
      fs.unlinkSync(path.join(logsDir, file));
    });
    fs.rmdirSync(logsDir);
    console.log(`✅ Removed logs directory with ${logFiles.length} files`);
    removedCount += logFiles.length + 1;
  } catch (error) {
    console.log(`❌ Failed to remove logs directory: ${error.message}`);
  }
}

// List remaining files
console.log('\n📋 Files remaining in directory:');
const remainingFiles = fs.readdirSync(__dirname).filter(file => {
  const stat = fs.statSync(path.join(__dirname, file));
  return stat.isFile() && !file.startsWith('.');
});

remainingFiles.forEach(file => {
  if (keepFiles.includes(file)) {
    console.log(`✅ Kept: ${file}`);
    keptCount++;
  } else if (file === 'finmanager-bot.js') {
    console.log(`🎯 MAIN BOT: ${file} (THE ONLY BOT YOU NEED!)`);
    keptCount++;
  } else if (file === 'cleanup-old-bots.js') {
    console.log(`🧹 Cleanup script: ${file}`);
  } else {
    console.log(`⚪ Other: ${file}`);
  }
});

// Summary
console.log('\n🎊 Cleanup Complete!');
console.log(`📊 Summary:`);
console.log(`   • Files removed: ${removedCount}`);
console.log(`   • Files kept: ${keptCount}`);
console.log(`   • Main bot file: finmanager-bot.js`);

console.log('\n🎯 RESULT: You now have ONE SINGLE BOT with ALL features!');
console.log('\n📁 Essential files remaining:');
console.log('   🤖 finmanager-bot.js - THE COMPLETE BOT (all features included)');
console.log('   📦 package.json - Dependencies');
console.log('   📋 README.md - Documentation');
console.log('   🚀 start-bot.bat - Quick start script');
console.log('   📚 Implementation guides - Complete documentation');

console.log('\n✅ No more confusion - just ONE bot file with EVERYTHING!');
console.log('\n🚀 To start your bot:');
console.log('   node finmanager-bot.js');
console.log('\n   OR');
console.log('\n   start-bot.bat');

console.log('\n🎉 Ready for local testing and deployment!');
