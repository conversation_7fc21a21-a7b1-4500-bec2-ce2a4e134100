#!/usr/bin/env node
/**
 * Telegram Bot Performance Diagnostic Tool
 * 
 * This script analyzes the bot's performance issues and provides optimization recommendations.
 */

require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 Starting Telegram Bot Performance Diagnostic...\n');

class BotPerformanceDiagnostic {
  constructor() {
    this.results = {
      initialization: {},
      database: {},
      commands: {},
      nlp: {},
      services: {},
      recommendations: []
    };
  }

  async runDiagnostic() {
    console.log('📊 Running comprehensive performance diagnostic...\n');

    try {
      // Test 1: Bot Initialization Performance
      await this.testBotInitialization();

      // Test 2: Database Connection Performance
      await this.testDatabasePerformance();

      // Test 3: Command Response Times
      await this.testCommandPerformance();

      // Test 4: NLP Processing Performance
      await this.testNLPPerformance();

      // Test 5: Service Loading Performance
      await this.testServicePerformance();

      // Generate recommendations
      this.generateRecommendations();

      // Display results
      this.displayResults();

    } catch (error) {
      console.error('❌ Diagnostic failed:', error.message);
    }
  }

  async testBotInitialization() {
    console.log('🚀 Testing bot initialization performance...');
    const startTime = Date.now();

    try {
      // Test basic bot creation
      const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: false });
      const initTime = Date.now() - startTime;

      this.results.initialization = {
        success: true,
        initTime: initTime,
        status: initTime < 1000 ? 'Good' : initTime < 3000 ? 'Moderate' : 'Slow'
      };

      console.log(`✅ Bot initialization: ${initTime}ms (${this.results.initialization.status})`);

    } catch (error) {
      this.results.initialization = {
        success: false,
        error: error.message,
        status: 'Failed'
      };
      console.log(`❌ Bot initialization failed: ${error.message}`);
    }
  }

  async testDatabasePerformance() {
    console.log('🗄️ Testing database connection performance...');

    try {
      const supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      // Test 1: Basic connection
      const startTime = Date.now();
      const { data, error } = await supabase.from('categories').select('count').limit(1);
      const connectionTime = Date.now() - startTime;

      if (error) throw error;

      // Test 2: Categories query (most common)
      const categoriesStart = Date.now();
      const { data: categories, error: catError } = await supabase
        .from('categories')
        .select('*')
        .limit(50);
      const categoriesTime = Date.now() - categoriesStart;

      if (catError) throw catError;

      // Test 3: User authentication query
      const authStart = Date.now();
      const { data: users, error: userError } = await supabase
        .from('telegram_users')
        .select('*')
        .limit(10);
      const authTime = Date.now() - authStart;

      if (userError) throw userError;

      this.results.database = {
        success: true,
        connectionTime: connectionTime,
        categoriesQueryTime: categoriesTime,
        authQueryTime: authTime,
        status: connectionTime < 500 ? 'Good' : connectionTime < 1500 ? 'Moderate' : 'Slow'
      };

      console.log(`✅ Database connection: ${connectionTime}ms`);
      console.log(`✅ Categories query: ${categoriesTime}ms`);
      console.log(`✅ Auth query: ${authTime}ms`);

    } catch (error) {
      this.results.database = {
        success: false,
        error: error.message,
        status: 'Failed'
      };
      console.log(`❌ Database test failed: ${error.message}`);
    }
  }

  async testCommandPerformance() {
    console.log('⚡ Testing command processing performance...');

    const commands = [
      { name: 'start', complexity: 'Low' },
      { name: 'help', complexity: 'Low' },
      { name: 'categories', complexity: 'High' },
      { name: 'balance', complexity: 'Medium' },
      { name: 'recent', complexity: 'High' }
    ];

    this.results.commands = {};

    for (const command of commands) {
      try {
        const startTime = Date.now();
        
        // Simulate command processing time based on complexity
        if (command.name === 'categories') {
          // Simulate categories database query
          await this.simulateCategoriesCommand();
        } else if (command.name === 'balance' || command.name === 'recent') {
          // Simulate transaction queries
          await this.simulateTransactionCommand();
        } else {
          // Simple commands
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        const processingTime = Date.now() - startTime;
        
        this.results.commands[command.name] = {
          success: true,
          processingTime: processingTime,
          complexity: command.complexity,
          status: processingTime < 1000 ? 'Good' : processingTime < 3000 ? 'Moderate' : 'Slow'
        };

        console.log(`✅ /${command.name} command: ${processingTime}ms (${command.complexity})`);

      } catch (error) {
        this.results.commands[command.name] = {
          success: false,
          error: error.message,
          complexity: command.complexity,
          status: 'Failed'
        };
        console.log(`❌ /${command.name} command failed: ${error.message}`);
      }
    }
  }

  async simulateCategoriesCommand() {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Simulate the actual categories query
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) throw error;

    // Simulate processing categories
    const expenseCategories = data?.filter(cat => cat.type === 'expense') || [];
    const incomeCategories = data?.filter(cat => cat.type === 'income') || [];

    // Simulate message formatting
    await new Promise(resolve => setTimeout(resolve, 100));

    return { expenseCategories, incomeCategories };
  }

  async simulateTransactionCommand() {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Simulate transaction query
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .limit(10)
      .order('created_at', { ascending: false });

    if (error && !error.message.includes('does not exist')) {
      throw error;
    }

    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 200));

    return data || [];
  }

  async testNLPPerformance() {
    console.log('🧠 Testing NLP processing performance...');

    const testMessages = [
      'Spent 500 on food',
      'Paid 1200 for groceries at BigBasket',
      'Received 50000 salary',
      'Amazon order 899 for electronics',
      'Coffee 150'
    ];

    this.results.nlp = {};

    for (const message of testMessages) {
      try {
        const startTime = Date.now();
        
        // Simulate NLP processing
        const result = this.simulateNLPProcessing(message);
        
        const processingTime = Date.now() - startTime;
        
        this.results.nlp[message] = {
          success: result.success,
          processingTime: processingTime,
          confidence: result.confidence,
          status: processingTime < 100 ? 'Good' : processingTime < 300 ? 'Moderate' : 'Slow'
        };

        console.log(`✅ NLP "${message}": ${processingTime}ms (${result.confidence}% confidence)`);

      } catch (error) {
        this.results.nlp[message] = {
          success: false,
          error: error.message,
          status: 'Failed'
        };
        console.log(`❌ NLP "${message}" failed: ${error.message}`);
      }
    }
  }

  simulateNLPProcessing(message) {
    const lowerMessage = message.toLowerCase();

    // Extract amount
    const amountMatch = message.match(/(\d+(?:,\d{3})*(?:\.\d{2})?)/);
    const amount = amountMatch ? parseFloat(amountMatch[1].replace(/,/g, '')) : null;

    // Determine type
    const expenseKeywords = ['spent', 'paid', 'bought', 'purchase', 'expense', 'cost', 'bill'];
    const incomeKeywords = ['earned', 'received', 'income', 'salary', 'got', 'profit'];
    
    const hasExpenseKeyword = expenseKeywords.some(keyword => lowerMessage.includes(keyword));
    const hasIncomeKeyword = incomeKeywords.some(keyword => lowerMessage.includes(keyword));
    
    const type = hasExpenseKeyword ? 'expense' : hasIncomeKeyword ? 'income' : null;

    // Extract category
    const categories = ['food', 'groceries', 'salary', 'electronics', 'coffee'];
    const category = categories.find(cat => lowerMessage.includes(cat)) || 'other';

    const success = amount && type && category;
    const confidence = success ? 85 : 30;

    return {
      success,
      confidence,
      transaction: success ? { amount, type, category } : null
    };
  }

  async testServicePerformance() {
    console.log('🔧 Testing service loading performance...');

    const services = [
      'MonitoringService',
      'I18nService',
      'SecurityService',
      'TelegramGCSService',
      'RealVoiceService',
      'PushNotificationService',
      'EnhancedConfirmationService',
      'AdvancedObservabilityService',
      'CollaborationService',
      'PredictiveAlertSystem',
      'AdvancedVoiceIntelligence',
      'SmartSchedulingAI'
    ];

    this.results.services = {};

    for (const service of services) {
      try {
        const startTime = Date.now();
        
        // Check if service file exists
        const servicePath = `../scripts/${service.toLowerCase().replace(/service$/, '-service')}.cjs`;
        
        try {
          require.resolve(servicePath);
          const loadTime = Date.now() - startTime;
          
          this.results.services[service] = {
            success: true,
            loadTime: loadTime,
            status: 'Available'
          };
          
          console.log(`✅ ${service}: Available`);
        } catch (requireError) {
          this.results.services[service] = {
            success: false,
            error: 'File not found',
            status: 'Missing'
          };
          
          console.log(`⚠️ ${service}: Missing`);
        }

      } catch (error) {
        this.results.services[service] = {
          success: false,
          error: error.message,
          status: 'Error'
        };
        
        console.log(`❌ ${service}: Error - ${error.message}`);
      }
    }
  }

  generateRecommendations() {
    console.log('\n💡 Generating performance recommendations...');

    // Database recommendations
    if (this.results.database.connectionTime > 1000) {
      this.results.recommendations.push({
        category: 'Database',
        priority: 'High',
        issue: 'Slow database connection',
        solution: 'Optimize Supabase connection pooling and add connection caching'
      });
    }

    if (this.results.database.categoriesQueryTime > 500) {
      this.results.recommendations.push({
        category: 'Database',
        priority: 'Medium',
        issue: 'Slow categories query',
        solution: 'Add database indexing on user_id and implement categories caching'
      });
    }

    // Command recommendations
    Object.entries(this.results.commands).forEach(([command, result]) => {
      if (result.processingTime > 2000) {
        this.results.recommendations.push({
          category: 'Commands',
          priority: 'High',
          issue: `Slow /${command} command (${result.processingTime}ms)`,
          solution: 'Implement command-specific optimizations and caching'
        });
      }
    });

    // Service recommendations
    const missingServices = Object.entries(this.results.services)
      .filter(([_, result]) => !result.success)
      .map(([service, _]) => service);

    if (missingServices.length > 0) {
      this.results.recommendations.push({
        category: 'Services',
        priority: 'High',
        issue: `Missing services: ${missingServices.join(', ')}`,
        solution: 'Remove or create missing service dependencies to prevent initialization delays'
      });
    }

    // NLP recommendations
    const slowNLP = Object.values(this.results.nlp).some(result => result.processingTime > 200);
    if (slowNLP) {
      this.results.recommendations.push({
        category: 'NLP',
        priority: 'Medium',
        issue: 'Slow NLP processing',
        solution: 'Optimize regex patterns and implement NLP result caching'
      });
    }
  }

  displayResults() {
    console.log('\n📊 PERFORMANCE DIAGNOSTIC RESULTS');
    console.log('=====================================\n');

    // Initialization Results
    console.log('🚀 Bot Initialization:');
    console.log(`   Status: ${this.results.initialization.status}`);
    if (this.results.initialization.success) {
      console.log(`   Time: ${this.results.initialization.initTime}ms`);
    } else {
      console.log(`   Error: ${this.results.initialization.error}`);
    }

    // Database Results
    console.log('\n🗄️ Database Performance:');
    console.log(`   Status: ${this.results.database.status}`);
    if (this.results.database.success) {
      console.log(`   Connection: ${this.results.database.connectionTime}ms`);
      console.log(`   Categories Query: ${this.results.database.categoriesQueryTime}ms`);
      console.log(`   Auth Query: ${this.results.database.authQueryTime}ms`);
    } else {
      console.log(`   Error: ${this.results.database.error}`);
    }

    // Command Results
    console.log('\n⚡ Command Performance:');
    Object.entries(this.results.commands).forEach(([command, result]) => {
      console.log(`   /${command}: ${result.status} (${result.processingTime}ms)`);
    });

    // Service Results
    console.log('\n🔧 Service Status:');
    Object.entries(this.results.services).forEach(([service, result]) => {
      console.log(`   ${service}: ${result.status}`);
    });

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('===================');
    this.results.recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}: ${rec.issue}`);
      console.log(`   Solution: ${rec.solution}`);
    });

    console.log('\n✅ Diagnostic complete!');
  }
}

// Run diagnostic
if (require.main === module) {
  const diagnostic = new BotPerformanceDiagnostic();
  diagnostic.runDiagnostic().catch(console.error);
}

module.exports = BotPerformanceDiagnostic;
