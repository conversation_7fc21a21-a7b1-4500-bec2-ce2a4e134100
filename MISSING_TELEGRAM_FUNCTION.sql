-- Missing Telegram Auth Code Function
-- This function is required for the frontend code generation process

-- Function to create temporary auth code (6-character, expires in 5 minutes)
CREATE OR REPLACE FUNCTION create_telegram_auth_code(p_user_id UUID)
RETURNS TEXT AS $$
DECLARE
  new_code TEXT;
  code_exists BOOLEAN;
BEGIN
  LOOP
    -- Generate 6-character temporary code using alphanumeric characters
    new_code := UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 6));
    
    -- Check if code already exists and is not expired
    SELECT EXISTS(
      SELECT 1 FROM telegram_auth_codes 
      WHERE code = new_code 
      AND expires_at > NOW()
      AND is_used = false
    ) INTO code_exists;
    
    -- If code doesn't exist, we can use it
    IF NOT code_exists THEN
      EXIT;
    END IF;
  END LOOP;
  
  -- Insert the new temporary code (expires in 5 minutes)
  INSERT INTO telegram_auth_codes (user_id, code, expires_at, is_used)
  VALUES (p_user_id, new_code, NOW() + INTERVAL '5 minutes', false);
  
  -- Clean up expired codes (optional housekeeping)
  DELETE FROM telegram_auth_codes 
  WHERE expires_at < NOW() - INTERVAL '1 hour';
  
  RETURN new_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_telegram_auth_code(UUID) TO authenticated;

-- Test the function (uncomment to test)
-- SELECT create_telegram_auth_code('550e8400-e29b-41d4-a716-************'::uuid);
