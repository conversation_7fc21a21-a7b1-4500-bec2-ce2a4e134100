# Bot Repository Reset Guide

## 🎯 **Objective**
Clean up the finmanager-bot repository to contain only bot-specific code and remove all web application files that were mistakenly pushed.

## 📋 **Current Situation**
- **finmanager-bot repository** contains both web app and bot code
- **Web app code** should be in FiNManageR repository only
- **Bot code** is in `finmanager-bot/` subdirectory
- **Need to clean up** and restructure the bot repository

## 🛠️ **Step-by-Step Cleanup Process**

### **Step 1: Backup Current Bot Files**
```bash
# Create backup directory
mkdir bot-backup-$(date +%Y%m%d-%H%M%S)

# Copy bot files
cp -r finmanager-bot/ bot-backup-*/
cp *.md bot-backup-*/docs/ 2>/dev/null || true
```

### **Step 2: Create Clean Bot Repository Structure**

#### **Files to Keep (Bot-Specific):**
```
finmanager-bot/
├── index.js                    # Simple bot
├── enterprise-bot.js           # Full-featured bot  
├── test-bot.js                # Configuration test
├── start-bot.bat              # Windows startup script
├── test-fast-bot.js           # Fast bot test
├── package.json               # Bot dependencies
├── .env.example               # Environment template
├── README.md                  # Bot documentation
├── Dockerfile                 # Container setup
├── docker-compose.yml         # Docker orchestration
└── deployment/                # Deployment configs
```

#### **Files to Remove (Web App):**
```
# Web App Source Code
src/
public/
index.html
vite.config.ts
tailwind.config.js
postcss.config.js
tsconfig.*.json
eslint.config.js

# Web App Dependencies
package-lock.json (web app version)
pnpm-lock.yaml
node_modules/

# Web App Documentation
ACCESSIBILITY.md
ARCHITECTURE.md
MOBILE_OPTIMIZATION.md
PERFORMANCE.md
ATTACHMENT_SYSTEM_*.md
GCS_DEPLOYMENT_GUIDE.md
GOOGLE_OAUTH_SETUP_GUIDE.md

# Web App Build/Test Files
coverage/
cypress/
tests/
.tsbuildinfo
*.html (performance fixes)

# Web App APIs
api/
netlify/
google-service-account.json
```

### **Step 3: Update Bot Configuration**

#### **package.json (Bot Version):**
```json
{
  "name": "finmanager-telegram-bot",
  "version": "1.0.0",
  "description": "FiNManageR Telegram Bot - Enterprise Financial Assistant",
  "main": "enterprise-bot.js",
  "type": "commonjs",
  "scripts": {
    "start": "node enterprise-bot.js",
    "dev": "nodemon enterprise-bot.js", 
    "simple": "node index.js",
    "test": "node test-bot.js",
    "health": "curl -f http://localhost:3001/health || exit 1"
  },
  "dependencies": {
    "node-telegram-bot-api": "^0.61.0",
    "@supabase/supabase-js": "^2.39.0",
    "dotenv": "^16.0.3",
    "express": "^4.18.2",
    "helmet": "^6.1.5",
    "cors": "^2.8.5",
    "compression": "^1.7.4",
    "winston": "^3.8.2",
    "multer": "^1.4.5-lts.1",
    "axios": "^1.4.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.22"
  }
}
```

#### **.env.example (Bot Version):**
```env
# FiNManageR Telegram Bot Configuration

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
BOT_USERNAME=Myfnmbot

# Supabase
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Server
PORT=3000
HEALTH_PORT=3001
NODE_ENV=production
```

#### **README.md (Bot Version):**
```markdown
# FiNManageR Telegram Bot

Enterprise Financial Assistant Telegram Bot for FiNManageR.

## 🚀 Quick Start

### Installation
npm install

### Configuration  
1. Copy .env.example to .env
2. Fill in bot token and Supabase credentials
3. Test: npm test

### Running
npm run simple    # Simple bot
npm start         # Enterprise bot
npm run dev       # Development mode

## 🤖 Features
- Real-time transaction logging
- Natural language processing
- Voice message support
- Receipt scanning (OCR)
- Smart categorization
- Balance tracking
- Spending insights

## 📱 Commands
/start - Welcome
/help - Commands
/link <code> - Link account
/expense <amount> <category> <description>
/income <amount> <category> <description>
/balance - Check balance
/recent - Recent transactions
/insights - AI analysis
```

### **Step 4: Git Repository Cleanup**

#### **Option A: Force Push Clean Repository**
```bash
# Create new orphan branch
git checkout --orphan clean-bot

# Add only bot files
git add index.js enterprise-bot.js test-bot.js package.json README.md .env.example
git add Dockerfile docker-compose.yml start-bot.bat
git add deployment/ scripts/ (bot-specific only)

# Commit clean version
git commit -m "Clean bot repository - remove web app files"

# Force push to main
git branch -D main
git branch -m main
git push -f origin main
```

#### **Option B: Manual Cleanup**
```bash
# Remove web app files
rm -rf src/ public/ index.html vite.config.ts tailwind.config.js
rm -rf coverage/ cypress/ tests/ api/ netlify/
rm -f *.html *.tsbuildinfo pnpm-lock.yaml
rm -f ACCESSIBILITY.md ARCHITECTURE.md MOBILE_OPTIMIZATION.md
rm -f ATTACHMENT_SYSTEM_*.md GCS_DEPLOYMENT_GUIDE.md

# Move bot files to root
mv finmanager-bot/* .
rmdir finmanager-bot/

# Update configurations
# (Update package.json, README.md, .env.example as shown above)

# Commit changes
git add .
git commit -m "Clean up bot repository structure"
git push origin main
```

## ✅ **Verification Steps**

### **1. Repository Structure Check**
```bash
# Should see only bot files
ls -la
# Expected: index.js, enterprise-bot.js, package.json, README.md, etc.
# Should NOT see: src/, public/, vite.config.ts, etc.
```

### **2. Bot Functionality Test**
```bash
# Install dependencies
npm install

# Test configuration
npm test

# Start simple bot
npm run simple

# Test in Telegram with @Myfnmbot
```

### **3. Repository Size Check**
```bash
# Should be much smaller without web app files
du -sh .
# Expected: < 50MB (vs previous ~200MB+)
```

## 🎯 **Expected Results**

### **Before Cleanup:**
- ❌ Repository contains both web app and bot code
- ❌ Large repository size (~200MB+)
- ❌ Confusing file structure
- ❌ Mixed dependencies

### **After Cleanup:**
- ✅ Repository contains only bot code
- ✅ Smaller repository size (~50MB)
- ✅ Clear bot-focused structure
- ✅ Bot-specific dependencies only
- ✅ Clean documentation
- ✅ Easy deployment

## 🚀 **Deployment Ready**

After cleanup, the bot repository will be ready for:
- ✅ Oracle Cloud E2.1 Micro deployment
- ✅ Docker containerization
- ✅ PM2 process management
- ✅ Health monitoring
- ✅ Production scaling

The cleaned repository will be focused, efficient, and deployment-ready!
