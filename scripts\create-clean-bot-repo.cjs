#!/usr/bin/env node
/**
 * Create Clean Bot Repository Script
 * 
 * This script will create a clean bot repository with only bot-specific files
 * by extracting the bot code from the current mixed repository.
 */

const fs = require('fs').promises;
const path = require('path');

console.log('🤖 Creating Clean Bot Repository...\n');

// Bot files to extract and include
const BOT_FILES = {
  // Core bot files
  'finmanager-bot/index.js': 'index.js',
  'finmanager-bot/enterprise-bot.js': 'enterprise-bot.js', 
  'finmanager-bot/test-bot.js': 'test-bot.js',
  'finmanager-bot/start-bot.bat': 'start-bot.bat',
  'finmanager-bot/test-fast-bot.js': 'test-fast-bot.js',
  'finmanager-bot/FAST_BOT_DEPLOYMENT.md': 'FAST_BOT_DEPLOYMENT.md',
  
  // Configuration files
  'Dockerfile': 'Dockerfile',
  'docker-compose.yml': 'docker-compose.yml',
  
  // Keep essential directories
  'deployment/': 'deployment/',
  'monitoring/': 'monitoring/',
  'config/': 'config/',
  
  // Bot-relevant documentation
  'DATABASE_SETUP.md': 'DATABASE_SETUP.md',
  'DOCKER_SETUP.md': 'DOCKER_SETUP.md',
  'LOCAL_DEVELOPMENT.md': 'LOCAL_DEVELOPMENT.md',
  'SECURITY.md': 'SECURITY.md',
  'CONTRIBUTING.md': 'CONTRIBUTING.md',
  'DEPENDENCIES.md': 'DEPENDENCIES.md'
};

// Create bot-specific package.json
const BOT_PACKAGE_JSON = {
  "name": "finmanager-telegram-bot",
  "version": "1.0.0",
  "description": "FiNManageR Telegram Bot - Enterprise Financial Assistant",
  "main": "enterprise-bot.js",
  "type": "commonjs",
  "scripts": {
    "start": "node enterprise-bot.js",
    "dev": "nodemon enterprise-bot.js",
    "simple": "node index.js",
    "test": "node test-bot.js",
    "health": "curl -f http://localhost:3001/health || exit 1"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "dependencies": {
    "node-telegram-bot-api": "^0.61.0",
    "@supabase/supabase-js": "^2.39.0",
    "dotenv": "^16.0.3",
    "express": "^4.18.2",
    "helmet": "^6.1.5",
    "cors": "^2.8.5",
    "compression": "^1.7.4",
    "winston": "^3.8.2",
    "multer": "^1.4.5-lts.1",
    "axios": "^1.4.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.22"
  },
  "keywords": [
    "telegram",
    "bot",
    "finance",
    "finmanager",
    "nodejs"
  ],
  "author": "bbsivajibb7",
  "license": "PRIVATE"
};

// Bot-specific README
const BOT_README = `# FiNManageR Telegram Bot

Enterprise Financial Assistant Telegram Bot for FiNManageR.

## 🚀 Quick Start

### Installation
\`\`\`bash
npm install
\`\`\`

### Configuration
1. Copy \`.env.example\` to \`.env\`
2. Fill in your bot token and Supabase credentials:
   - \`TELEGRAM_BOT_TOKEN\` - Get from @BotFather
   - \`SUPABASE_URL\` - Your Supabase project URL
   - \`SUPABASE_SERVICE_ROLE_KEY\` - Service role key
3. Test configuration: \`npm test\`

### Running the Bot
\`\`\`bash
# Simple bot (recommended for testing)
npm run simple

# Full enterprise bot
npm start

# Development with auto-reload
npm run dev
\`\`\`

## 🤖 Bot Features

- ✅ **Real-time Transaction Logging** - Log expenses and income instantly
- ✅ **Natural Language Processing** - "Spent 500 on food" → automatic transaction
- ✅ **Voice Message Support** - Send voice notes for hands-free logging
- ✅ **Receipt Scanning (OCR)** - Photo receipts automatically processed
- ✅ **Smart Categorization** - AI-powered expense categorization
- ✅ **Balance Tracking** - Real-time balance updates
- ✅ **Spending Insights** - AI-powered financial analysis
- ✅ **Multi-language Support** - Multiple language support
- ✅ **Enterprise Security** - Rate limiting, input validation, secure data handling

## 📱 Available Commands

### **Basic Commands**
- \`/start\` - Welcome message and setup
- \`/help\` - Complete command list
- \`/status\` - Bot and account status

### **Account Management**
- \`/link <8-digit-code>\` - Link your FiNManageR account
- \`/unlink\` - Unlink Telegram from FiNManageR
- \`/account\` - View account information

### **Transaction Commands**
- \`/expense <amount> <category> <description>\` - Log expense
- \`/income <amount> <category> <description>\` - Log income
- \`/balance\` - Check current balance
- \`/recent\` - View recent transactions

### **Analysis Commands**
- \`/insights\` - AI spending analysis
- \`/categories\` - View spending by category
- \`/monthly\` - Monthly spending summary

### **Natural Language Examples**
- "Spent 500 on food for lunch"
- "Paid 2000 for groceries"
- "Received 50000 salary"
- "Coffee 150"

## 🔧 Configuration

### **Required Environment Variables**
\`\`\`env
TELEGRAM_BOT_TOKEN=your_bot_token_here
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
\`\`\`

### **Optional Configuration**
\`\`\`env
PORT=3000
HEALTH_PORT=3001
NODE_ENV=production
LOG_LEVEL=info
BOT_USERNAME=Myfnmbot
\`\`\`

## 🚀 Deployment

### **Oracle Cloud E2.1 Micro (Recommended)**
1. Clone repository to your VM
2. Install Node.js 18+
3. Configure environment variables
4. Install dependencies: \`npm install\`
5. Start with PM2: \`pm2 start enterprise-bot.js --name finmanager-bot\`

### **Docker Deployment**
\`\`\`bash
# Build image
docker build -t finmanager-bot .

# Run container
docker run -d --name finmanager-bot --env-file .env -p 3000:3000 finmanager-bot
\`\`\`

### **Docker Compose**
\`\`\`bash
docker-compose up -d
\`\`\`

## 📊 Monitoring

### **Health Check**
- **Endpoint**: \`http://localhost:3001/health\`
- **Status**: Returns bot health and uptime information

### **Logging**
- **Location**: \`logs/\` directory
- **Levels**: error, warn, info, debug
- **Rotation**: Daily log rotation

## 🔒 Security Features

- **Rate Limiting** - Prevents spam and abuse
- **Input Validation** - Sanitizes all user inputs
- **Secure Data Handling** - Encrypted data transmission
- **Authentication** - Secure account linking
- **Error Handling** - Graceful error management

## 🛠️ Development

### **Project Structure**
\`\`\`
finmanager-bot/
├── index.js              # Simple bot for testing
├── enterprise-bot.js     # Full-featured production bot
├── test-bot.js          # Configuration test script
├── package.json         # Dependencies and scripts
├── Dockerfile           # Container configuration
├── docker-compose.yml   # Docker orchestration
├── deployment/          # Deployment configurations
├── monitoring/          # Monitoring and logging
└── logs/               # Application logs
\`\`\`

### **Testing**
\`\`\`bash
# Test configuration
npm test

# Test simple bot
npm run simple

# Test with @Myfnmbot on Telegram
\`\`\`

## 📝 Bot Information

- **Bot Username**: @Myfnmbot
- **Production Token**: **********:AAGbbBrZDY400gWlHSIBMgToFDav1XnalKE
- **Database**: Connected to FiNManageR Supabase
- **Environment**: Production (used for all deployments)

## 🔗 Integration

This bot integrates with the main FiNManageR web application:
- **Web App**: https://github.com/bbsivajibb7/FiNManageR
- **Bot Repo**: https://github.com/bbsivajibb7/finmanager-bot
- **Live App**: https://finmanager.netlify.app

## 📞 Support

For issues and support:
1. Check the logs: \`logs/\` directory
2. Test configuration: \`npm test\`
3. Review deployment guides in \`deployment/\`
4. Check health endpoint: \`/health\`

## 📄 License

Private - FiNManageR Project
`;

// Bot-specific .env.example
const BOT_ENV_EXAMPLE = `# FiNManageR Telegram Bot Configuration

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
BOT_USERNAME=Myfnmbot

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Server Configuration
PORT=3000
HEALTH_PORT=3001
NODE_ENV=production

# Optional Configuration
LOG_LEVEL=info
`;

// Bot-specific .gitignore
const BOT_GITIGNORE = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment
.env
.env.local
.env.production

# Logs
logs/
*.log

# Runtime
.pid
.seed
*.pid.lock

# Temporary files
temp/
uploads/
*.tmp

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Bot specific
bot-backup-*/
`;

async function createCleanBotRepo() {
  try {
    const outputDir = 'clean-bot-repo';
    
    console.log(`📁 Creating clean bot repository in ${outputDir}/...`);
    
    // Create output directory
    await fs.mkdir(outputDir, { recursive: true });
    
    // Copy bot files
    console.log('📋 Copying bot files...');
    for (const [srcPath, destPath] of Object.entries(BOT_FILES)) {
      try {
        if (await fileExists(srcPath)) {
          const fullDestPath = path.join(outputDir, destPath);
          
          // Create directory if needed
          await fs.mkdir(path.dirname(fullDestPath), { recursive: true });
          
          // Copy file or directory
          const stats = await fs.stat(srcPath);
          if (stats.isDirectory()) {
            await copyDirectory(srcPath, fullDestPath);
            console.log(`✅ Copied directory: ${srcPath} → ${destPath}`);
          } else {
            await fs.copyFile(srcPath, fullDestPath);
            console.log(`✅ Copied file: ${srcPath} → ${destPath}`);
          }
        }
      } catch (error) {
        console.log(`⚠️ Could not copy ${srcPath}: ${error.message}`);
      }
    }
    
    // Create bot-specific files
    console.log('\n📝 Creating bot-specific configuration files...');
    
    await fs.writeFile(
      path.join(outputDir, 'package.json'),
      JSON.stringify(BOT_PACKAGE_JSON, null, 2)
    );
    console.log('✅ Created package.json');
    
    await fs.writeFile(path.join(outputDir, 'README.md'), BOT_README);
    console.log('✅ Created README.md');
    
    await fs.writeFile(path.join(outputDir, '.env.example'), BOT_ENV_EXAMPLE);
    console.log('✅ Created .env.example');
    
    await fs.writeFile(path.join(outputDir, '.gitignore'), BOT_GITIGNORE);
    console.log('✅ Created .gitignore');
    
    // Create necessary directories
    const botDirs = ['logs', 'temp', 'uploads'];
    for (const dir of botDirs) {
      await fs.mkdir(path.join(outputDir, dir), { recursive: true });
      console.log(`✅ Created directory: ${dir}/`);
    }
    
    console.log('\n✅ Clean bot repository created successfully!');
    console.log(`\n📊 Repository contents in ${outputDir}/:`);
    
    const files = await fs.readdir(outputDir);
    for (const file of files.sort()) {
      const stats = await fs.stat(path.join(outputDir, file));
      const type = stats.isDirectory() ? '📁' : '📄';
      console.log(`${type} ${file}`);
    }
    
    console.log('\n🚀 Next steps:');
    console.log(`1. cd ${outputDir}`);
    console.log('2. npm install');
    console.log('3. cp .env.example .env');
    console.log('4. Edit .env with your bot token and Supabase credentials');
    console.log('5. npm test');
    console.log('6. npm run simple');
    console.log('7. Test with @Myfnmbot on Telegram');
    console.log('\n📤 To deploy this clean repository:');
    console.log('1. Initialize git: git init');
    console.log('2. Add files: git add .');
    console.log('3. Commit: git commit -m "Clean bot repository"');
    console.log('4. Add remote: git remote add origin https://github.com/bbsivajibb7/finmanager-bot.git');
    console.log('5. Force push: git push -f origin main');
    
  } catch (error) {
    console.error('❌ Error creating clean bot repository:', error.message);
    process.exit(1);
  }
}

// Helper functions
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function copyDirectory(src, dest) {
  await fs.mkdir(dest, { recursive: true });
  const entries = await fs.readdir(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.copyFile(srcPath, destPath);
    }
  }
}

// Run the script
if (require.main === module) {
  createCleanBotRepo();
}

module.exports = { createCleanBotRepo };
