import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, AlertCircle } from 'lucide-react';
import { useBudget } from '../../contexts/BudgetContext';
import { useCurrency } from '../../contexts/CurrencyContext';

interface BudgetStatusProps {
  category: string;
  amount: string;
}

export function BudgetStatus({ category, amount }: BudgetStatusProps) {
  const { budgets } = useBudget();
  const { formatCurrency } = useCurrency();
  const [budgetStatus, setBudgetStatus] = useState<{
    budget: any;
    spent: number;
    remaining: number;
    percentage: number;
  } | null>(null);

  useEffect(() => {
    if (!category || !amount || parseFloat(amount) <= 0) {
      setBudgetStatus(null);
      return;
    }

    // Find a budget for this category
    const relevantBudget = budgets.find(budget =>
      budget.category === category &&
      new Date(budget.period_start) <= new Date() &&
      new Date(budget.period_end) >= new Date()
    );

    if (!relevantBudget) {
      setBudgetStatus(null);
      return;
    }

    // Calculate budget status
    const amountValue = parseFloat(amount);
    const spent = relevantBudget.spent || 0;
    const remaining = relevantBudget.amount - spent;
    const newRemaining = remaining - amountValue;
    const percentage = Math.round((spent + amountValue) / relevantBudget.amount * 100);

    setBudgetStatus({
      budget: relevantBudget,
      spent: spent + amountValue,
      remaining: newRemaining,
      percentage
    });

  }, [category, amount, budgets]);

  if (!budgetStatus) return null;

  // Determine status color based on percentage
  const getStatusColor = () => {
    if (budgetStatus.percentage >= 100) return 'text-red-600 dark:text-red-400';
    if (budgetStatus.percentage >= 80) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  return (
    <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-start">
        <PieChart className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
        <div className="w-full">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Budget Impact
          </h4>

          <div className="mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-500 dark:text-gray-400">Budget for {category}:</span>
              <span className="font-medium">{formatCurrency(budgetStatus.budget.amount)}</span>
            </div>

            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-500 dark:text-gray-400">Already spent:</span>
              <span className="font-medium">{formatCurrency(budgetStatus.spent - parseFloat(amount))}</span>
            </div>

            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-500 dark:text-gray-400">This transaction:</span>
              <span className="font-medium">{formatCurrency(parseFloat(amount))}</span>
            </div>

            <div className="flex justify-between text-xs mb-2">
              <span className="text-gray-500 dark:text-gray-400">Remaining after transaction:</span>
              <span className={`font-medium ${budgetStatus.remaining < 0 ? 'text-red-600 dark:text-red-400' : ''}`}>
                {formatCurrency(budgetStatus.remaining)}
              </span>
            </div>

            {/* Progress bar */}
            <div className="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full ${
                  budgetStatus.percentage >= 100
                    ? 'bg-red-500'
                    : budgetStatus.percentage >= 80
                      ? 'bg-yellow-500'
                      : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(budgetStatus.percentage, 100)}%` }}
              />
            </div>

            <div className="flex justify-between items-center mt-1">
              <span className={`text-xs font-medium ${getStatusColor()}`}>
                {budgetStatus.percentage}% of budget
              </span>

              {budgetStatus.percentage >= 100 && (
                <div className="flex items-center text-xs text-red-600 dark:text-red-400">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Budget exceeded
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
