import React, { useEffect, useState } from 'react';
import { AttachmentInput } from './AttachmentInput';
import { addTransaction } from '../lib/transactions';
import { useToast } from './ui/use-toast';
import { useAuth } from '../contexts/AuthContext';
import { usePaymentMethod } from '../contexts/PaymentMethodContext';
import { useCategories } from '../contexts/CategoryContext';
import { useBudget } from '../contexts/BudgetContext';
import { uploadAttachment } from '../lib/uploadAttachment';
import { useNotificationTriggers } from '../hooks/useNotificationTriggers';
import { showSuccessNotification, showErrorNotification } from '../lib/utils/notifications';



interface TransactionFormProps {
  userId: string;
  onSubmit?: (data: any) => void;
  type: 'income' | 'expense';
  onCancel?: () => void;
}

export function TransactionForm({ userId: userIdProp, type, onSubmit, onCancel }: TransactionFormProps) {
  const { user } = useAuth();
  const { paymentMethods, refreshPaymentMethods } = usePaymentMethod();
  const { expenseCategories, incomeCategories, addCategory, checkCategoryExists, loading: categoriesLoading } = useCategories();
  const { budgets, recentTransactions } = useBudget();
  const { triggerTransactionAdded, checkBudgetStatus } = useNotificationTriggers();
  const userId = user?.id || userIdProp || '';

  // Find default payment method
  const defaultPaymentMethod = paymentMethods.find(method => method.isDefault)?.name || 'Cash';

  const [formData, setFormData] = useState({
    amount: '',
    category: '',
    source: '',
    paymentMethod: defaultPaymentMethod,
    date: new Date().toISOString().slice(0, 10),
    description: '',
    location: '',
    attachmentUrl: null as string | null,
    attachments: [] as string[],
  });
  const [customCategory, setCustomCategory] = useState('');
  const [showCustomCategory, setShowCustomCategory] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurringFrequency, setRecurringFrequency] = useState('monthly');
  const [recurringEndDate, setRecurringEndDate] = useState('');
  const { toast } = useToast();

  // Get categories based on transaction type
  const categories = type === 'expense' ? expenseCategories : incomeCategories;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }
    if (!formData.category) {
      newErrors.category = showCustomCategory ? 'Please enter a custom category' : 'Please select a category';
    }
    if (!formData.source) {
      newErrors.source = type === 'income' ? 'Please enter income source' : 'Please enter merchant/vendor';
    }
    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Please select payment method';
    }
    if (!formData.date) {
      newErrors.date = 'Please select a date';
    }

    // Format validation for amount (no more than 2 decimal places)
    if (formData.amount && !/^\d+(\.\d{1,2})?$/.test(formData.amount)) {
      newErrors.amount = 'Amount should have maximum 2 decimal places';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (name === 'category') {
      if (value === '__custom__') {
        setShowCustomCategory(true);
        setFormData(prev => ({ ...prev, category: '' }));
      } else {
        setShowCustomCategory(false);
        setFormData(prev => ({ ...prev, category: value }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleCustomCategoryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomCategory(e.target.value);
    setFormData(prev => ({ ...prev, category: e.target.value }));
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: '' }));
    }
  };

  const handleAddCustomCategory = async () => {
    const trimmedName = customCategory.trim();
    if (!trimmedName) {
      showErrorNotification(
        'Invalid Category',
        'Category name cannot be empty',
        { duration: 3000 }
      );
      return;
    }

    // Check if category already exists
    if (checkCategoryExists(trimmedName, type)) {
      showErrorNotification(
        'Duplicate Category',
        `A category named "${trimmedName}" already exists for ${type} transactions`,
        { duration: 5000 }
      );
      return;
    }

    try {
      // Add the category to the database via CategoryContext
      await addCategory({
        name: trimmedName,
        type: type,
        isDefault: false,
      });

      // Update form data with the new category
      setShowCustomCategory(false);
      setFormData(prev => ({ ...prev, category: trimmedName }));
      setCustomCategory('');

      showSuccessNotification(
        'Category Added',
        `Category "${trimmedName}" has been added successfully`,
        { duration: 3000 }
      );
    } catch (error: any) {
      // Error handling is now done in CategoryContext
      console.error('Category creation failed:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }
    setLoading(true);
    try {
      if (isRecurring) {
        const recurTemplate = {
          amount: parseFloat(formData.amount),
          category: formData.category,
          source: formData.source,
          type,
          description: formData.description || '',
          attachments: formData.attachments.length > 0 ? formData.attachments :
                      (formData.attachmentUrl ? [formData.attachmentUrl] : []),
        };
        const recurPayload = {
          user_id: userId,
          transaction_template: recurTemplate,
          frequency: recurringFrequency as 'daily' | 'weekly' | 'monthly' | 'yearly',
          start_date: formData.date,
          end_date: recurringEndDate || null,
          last_generated: formData.date,
        };
        try {
          const response = await fetch('/api/recurring_transactions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(recurPayload),
          });
          if (!response.ok) {
            throw new Error('Failed to create recurring transaction');
          }
          const data = await response.json();

          showSuccessNotification(
            'Recurring Transaction Scheduled',
            `Your ${type} will be automatically generated ${recurringFrequency}.`,
            { duration: 5000 }
          );

          if (onSubmit) onSubmit(data);
        } catch (error) {
          console.error('Error creating recurring transaction:', error);

          showErrorNotification(
            'Scheduling Failed',
            'Failed to create recurring transaction. Please try again.',
            { duration: 5000 }
          );
        }
        setLoading(false);
        return;
      }
      // Not recurring: create normal transaction
      const transactionData = {
        user_id: userId,
        amount: parseFloat(formData.amount),
        category: formData.category,
        source: formData.source,
        date: formData.date,
        type,
        description: formData.description || null,
        attachments: formData.attachments.length > 0 ? formData.attachments :
                    (formData.attachmentUrl ? [formData.attachmentUrl] : []),
        // Add payment method and location to metadata
        metadata: {
          paymentMethod: type === 'expense' ? formData.paymentMethod : null,
          location: formData.location || null,
        },
      };
      const result = await addTransaction(transactionData);

      // If this is a credit card transaction, refresh payment methods to update balances
      if (type === 'expense' && formData.paymentMethod) {
        try {
          await refreshPaymentMethods();
        } catch (refreshErr) {
          console.error('Error refreshing payment methods:', refreshErr);
          // Continue even if refresh fails
        }
      }

      // Format amount for display
      const formattedAmount = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(parseFloat(formData.amount));

      // Create a more detailed success message
      const successTitle = type === 'income'
        ? 'Income Added Successfully'
        : 'Expense Recorded Successfully';

      const successDescription = type === 'income'
        ? `Income of ${formattedAmount} from ${formData.source} has been added.`
        : `Expense of ${formattedAmount} for ${formData.category} has been recorded.`;

      showSuccessNotification(
        successTitle,
        successDescription,
        { duration: 4000 }
      );

      // Trigger transaction notification
      await triggerTransactionAdded({
        id: result.id,
        amount: parseFloat(formData.amount),
        type: type as 'income' | 'expense',
        category: formData.category
      });

      // Check budget thresholds for expenses
      if (type === 'expense' && budgets && budgets.length > 0) {
        // Calculate current spending for each budget
        const budgetData = budgets.map(budget => ({
          id: budget.id,
          name: budget.category,
          limit: budget.amount,
          spent: budget.spent + parseFloat(formData.amount) // Add current transaction
        }));

        // Check budget status and trigger notifications if needed
        await checkBudgetStatus(budgetData);
      }

      setFormData({
        amount: '',
        category: '',
        source: '',
        paymentMethod: 'Cash',
        date: new Date().toISOString().slice(0, 10),
        description: '',
        location: '',
        attachmentUrl: null,
        attachments: [],
      });

      onSubmit?.(result);
    } catch (err: any) {
      console.error('Error adding transaction:', err);

      showErrorNotification(
        'Transaction Failed',
        err.message || 'Failed to add transaction. Please try again.',
        { duration: 5000 }
      );
    } finally {
      setLoading(false);
    }
  };

  const [isMinimized, setIsMinimized] = useState(false);
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onCancel?.();
    };
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [onCancel]);

  return (
    <>
      {/* Overlay/modal or sidebar depending on isMinimized */}
      <div
        className={
          isMinimized
            ? 'fixed top-0 right-0 h-full w-[340px] bg-white dark:bg-gray-900 shadow-2xl z-50 transition-all'
            : 'fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-40'
        }
        style={isMinimized ? { minWidth: 220, maxWidth: 340 } : {}}
      >
        <form
          onSubmit={handleSubmit}
          className={`relative w-[90%] mx-auto max-w-md bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-4 space-y-3 transition-all ${isMinimized ? 'h-full' : ''}`}
          style={isMinimized ? { minWidth: 220, maxWidth: 340, height: '100%' } : { minWidth: 220, maxWidth: 340 }}
        >
          {/* Header with minimize and close */}
          <div className="flex items-center justify-between mb-1">
            <h2 className="block text-xs font-bold text-black tracking-wide dark:text-gray-100">Add Transaction</h2>
            <div className="flex gap-2">
              <button
                type="button"
                aria-label={isMinimized ? 'Restore' : 'Minimize'}
                className="text-gray-400 hover:text-gray-700 text-xs px-1"
                onClick={() => setIsMinimized(m => !m)}
                tabIndex={0}
              >
                {isMinimized ? <span title="Restore">&#9633;</span> : <span title="Minimize">&#8211;</span>}
              </button>
              <button
                type="button"
                aria-label="Close"
                className="text-gray-400 hover:text-gray-700 text-xs px-1"
                onClick={onCancel}
                tabIndex={0}
              >
                &#10005;
              </button>
            </div>
          </div>

        {/* Amount */}
        <div>
          <label htmlFor="amount" className="block text-sm font-medium mb-1 text-black dark:text-white">Amount</label>
          <input
            id="amount"
            name="amount"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            value={formData.amount}
            onChange={handleChange}
            className="w-[90%] mx-auto h-7 rounded border border-gray-300 dark:border-gray-700 px-1 text-sm text-black dark:text-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-700"
            disabled={loading}
          />
          {errors.amount && <p className="text-red-500 text-xs mt-0.5">{errors.amount}</p>}
        </div>

        {/* Category */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium mb-1 text-black dark:text-white">Category</label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            className="w-[90%] mx-auto h-7 rounded border border-gray-300 px-1 text-sm bg-white dark:bg-gray-800 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-200"
            disabled={loading || categoriesLoading}
          >
            <option value="">Select category</option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.name}>{cat.name}</option>
            ))}
            <option value="__custom__">Add new category...</option>
          </select>
          {showCustomCategory && (
            <div className="flex flex-col gap-1 mt-0.5">
              <input
                type="text"
                value={customCategory}
                onChange={handleCustomCategoryChange}
                className="w-[90%] mx-auto text-sm font-semibold text-black dark:text-white bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 px-1 py-0.5 rounded focus:outline-none focus:ring-2 focus:ring-yellow-400 dark:focus:ring-yellow-600 focus:bg-yellow-50 dark:focus:bg-yellow-900/20 transition-all disabled:bg-gray-200 dark:disabled:bg-gray-700 disabled:text-gray-500 dark:disabled:text-gray-400 disabled:border-gray-400 dark:disabled:border-gray-600 placeholder:text-gray-700 dark:placeholder:text-gray-400"
                disabled={loading || categoriesLoading}
                placeholder="Enter custom category"
              />
              <button
                type="button"
                className="bg-blue-600 text-white px-1 py-0.5 rounded-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed w-[90%] mx-auto sm:w-auto text-sm"
                onClick={handleAddCustomCategory}
                disabled={loading || categoriesLoading || !customCategory.trim()}
              >
                Add
              </button>
            </div>
          )}
          {errors.category && <p className="text-red-500 text-xs mt-0.5">{errors.category}</p>}
        </div>

        {/* Source */}
        <div>
          <label htmlFor="source" className="block text-sm font-medium mb-1 text-black dark:text-white">
            {type === 'income' ? 'Source' : 'Merchant/Vendor'}
          </label>
          <input
            id="source"
            name="source"
            type="text"
            placeholder={type === 'income' ? 'e.g., Company Name, Client' : 'e.g., Store Name, Vendor'}
            value={formData.source}
            onChange={handleChange}
            className="w-[90%] mx-auto h-7 rounded border border-gray-300 dark:border-gray-700 px-1 text-sm text-black dark:text-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-700"
            disabled={loading}
          />
          {errors.source && <p className="text-red-500 text-xs mt-0.5">{errors.source}</p>}
        </div>

        {/* Payment Method - Only show for expenses */}
        {type === 'expense' && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <label htmlFor="paymentMethod" className="block text-sm font-medium text-black dark:text-white">Payment Method</label>
              <a
                href="/settings"
                className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
              >
                Manage
              </a>
            </div>
            <select
              id="paymentMethod"
              name="paymentMethod"
              value={formData.paymentMethod}
              onChange={handleChange}
              className="w-[90%] mx-auto h-7 rounded border border-gray-300 px-1 text-sm bg-white dark:bg-gray-800 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-200"
              disabled={loading}
            >
              {paymentMethods.map((method) => (
                <option key={method.id} value={method.name}>
                  {method.icon ? `${method.name} ${method.icon}` : method.name}
                  {method.isDefault ? ' (Default)' : ''}
                </option>
              ))}
            </select>
            {errors.paymentMethod && <p className="text-red-500 text-xs mt-0.5">{errors.paymentMethod}</p>}
          </div>
        )}

        {/* Date */}
        <div>
          <label htmlFor="date" className="block text-sm font-medium mb-1 text-black dark:text-white">Date</label>
          <input
            id="date"
            name="date"
            type="date"
            value={formData.date}
            onChange={handleChange}
            className="w-[90%] mx-auto h-7 rounded border border-gray-300 dark:border-gray-700 px-1 text-sm text-black dark:text-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-700"
            disabled={loading}
          />
          {errors.date && <p className="text-red-500 text-xs mt-0.5">{errors.date}</p>}
        </div>

        {/* Recurring Checkbox (only for expense) */}
        {type === 'expense' && (
          <div className="flex items-center mt-0.5">
            <input
              id="recurring"
              type="checkbox"
              checked={isRecurring}
              onChange={e => setIsRecurring(e.target.checked)}
              className="mr-2 w-4 h-4 accent-blue-600"
              disabled={loading}
            />
            <label htmlFor="recurring" className="text-sm text-black dark:text-white">Is this a recurring expense?</label>
          </div>
        )}

        {/* Location */}
        <div>
          <label htmlFor="location" className="block text-sm font-medium mb-1 text-black dark:text-white">Location (Optional)</label>
          <input
            id="location"
            name="location"
            type="text"
            placeholder="e.g., City, Store Location"
            value={formData.location}
            onChange={handleChange}
            className="w-[90%] mx-auto h-7 rounded border border-gray-300 dark:border-gray-700 px-1 text-sm text-black dark:text-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-700"
            disabled={loading}
          />
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium mb-1 text-black dark:text-white">Description (Optional)</label>
          <textarea
            id="description"
            name="description"
            placeholder="Add any additional details..."
            value={formData.description}
            onChange={handleChange}
            className="w-[90%] mx-auto h-20 rounded border border-gray-300 dark:border-gray-700 px-1 py-1 text-sm text-black dark:text-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-700 resize-none"
            disabled={loading}
          />
        </div>

        {/* Attach file */}
        <div>
          <label htmlFor="attachment" className="block text-sm font-medium mb-1 text-black dark:text-white">
            Attach file <span className="font-normal text-gray-600 dark:text-gray-300">(Optional, max 5MB)</span>
          </label>
          <div className="w-[90%] mx-auto">
            <AttachmentInput
              userId={userId}
              onUpload={(url) => {
                setFormData(prev => ({
                  ...prev,
                  attachmentUrl: url,
                  attachments: [...prev.attachments, url]
                }));
              }}
              label=""
            />
          </div>
          {formData.attachments.length > 0 && (
            <div className="w-[90%] mx-auto mt-2">
              <div className="text-xs text-green-600 mb-1">✓ {formData.attachments.length} file(s) attached</div>
              <div className="max-h-20 overflow-y-auto bg-gray-50 dark:bg-gray-800 rounded p-1">
                {formData.attachments.map((url, index) => (
                  <div key={index} className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {url.includes('drive.google.com') ?
                      `File ${url.split('id=').pop()?.substring(0, 6)}...` :
                      `Attachment ${index + 1}`
                    }
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="w-[90%] mx-auto h-7 bg-blue-600 text-white text-xs font-semibold rounded mt-0.5 hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={loading}
        >
          {loading ? 'Saving...' : `Add ${type === 'income' ? 'Income' : 'Expense'}`}
        </button>

        {/* Cancel Button */}
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="block w-[90%] mx-auto mt-2 text-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm border border-gray-300 dark:border-gray-700 rounded-sm px-1 py-0.5"
            disabled={loading}
          >
            Cancel
          </button>
        )}
      </form>
    </div>
  </>
);
}
