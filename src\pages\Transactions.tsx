import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { useTransactionHistory } from '../contexts/TransactionHistoryContext';
import { useCategories } from '../contexts/CategoryContext';
import { usePaymentMethod } from '../contexts/PaymentMethodContext';
import { useLocation, useNavigate } from 'react-router-dom';
import { TransactionSearch } from '../components/TransactionSearch';
import { TransactionSorting } from '../components/TransactionSorting';
import { TransactionPagination } from '../components/TransactionPagination';
import { TransactionAnalytics } from '../components/TransactionAnalytics';
import { TransactionDetailsModal } from '../components/TransactionDetailsModal';
import { FilterModal } from '../components/FilterModal';
import { EnhancedTransactionForm } from '../components/EnhancedTransactionForm';
import { BulkExport } from '../components/BulkExport';
import { TransactionQuickFilters } from '../components/TransactionQuickFilters';
import { EnhancedTransactionTable } from '../components/EnhancedTransactionTable';
import { TransactionBulkOperations } from '../components/TransactionBulkOperations';
import { TransactionInsights } from '../components/TransactionInsights';
import { Filter, X, FileUp, Download } from 'lucide-react';
import { useToast } from '../components/ui/use-toast';
import { addTransaction } from '../lib/transactions';
import { getPaymentMethodName } from '../utils/PaymentMethodUtils';
interface TransactionFormProps {
  userId: string;
  onSubmit?: (data: any) => void;
  type: 'income' | 'expense';
  onCancel?: () => void;
}
export function TransactionForm({ userId: userIdProp, type, onSubmit, onCancel }: TransactionFormProps) {
  const { user } = useAuth();
  const { expenseCategories, incomeCategories, addCategory, checkCategoryExists, loading: categoriesLoading } = useCategories();
  const userId = user?.id || userIdProp;

  const [formData, setFormData] = useState({
    amount: '',
    category: '',
    source: '',
    date: new Date().toISOString().slice(0, 10),
    description: '',
    attachmentUrl: null as string | null,
  });
  const [customCategory, setCustomCategory] = useState('');
  const [showCustomCategory, setShowCustomCategory] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurringFrequency, setRecurringFrequency] = useState('monthly');
  const [recurringEndDate, setRecurringEndDate] = useState('');
  const { toast } = useToast();

  // Get categories based on transaction type
  const categories = type === 'expense' ? expenseCategories : incomeCategories;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }
    if (!formData.category) {
      newErrors.category = showCustomCategory ? 'Please enter a custom category' : 'Please select a category';
    }
    if (!formData.source) {
      newErrors.source = 'Please enter a source';
    }
    if (!formData.date) {
      newErrors.date = 'Please select a date';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    // Special handling for category select
    if (name === 'category') {
      if (value === '__custom__') {
        setShowCustomCategory(true);
        setFormData(prev => ({ ...prev, category: '' }));
      } else {
        setShowCustomCategory(false);
        setFormData(prev => ({ ...prev, category: value }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    // Clear error when field is modified
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle custom category input
  const handleCustomCategoryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomCategory(e.target.value);
    setFormData((prev: typeof formData) => ({ ...prev, category: e.target.value }));
    if (errors.category) {
      setErrors((prev: typeof errors) => ({ ...prev, category: '' }));
    }
  };

  // Add custom category to list and persist
  const handleAddCustomCategory = async () => {
    const trimmedName = customCategory.trim();
    if (!trimmedName) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    // Check if category already exists
    if (checkCategoryExists(trimmedName, type)) {
      toast({
        title: 'Duplicate Category',
        description: `A category named "${trimmedName}" already exists for ${type} transactions`,
        variant: 'destructive',
      });
      return;
    }

    try {
      // Add the category to the database via CategoryContext
      await addCategory({
        name: trimmedName,
        type: type,
        isDefault: false,
      });

      // Update form data with the new category
      setShowCustomCategory(false);
      setFormData(prev => ({ ...prev, category: trimmedName }));
      setCustomCategory('');

      toast({
        title: 'Success',
        description: `Category "${trimmedName}" added successfully`,
      });
    } catch (error: any) {
      // Error handling is now done in CategoryContext
      console.error('Category creation failed:', error);
    }
  };


  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }
    setLoading(true);
    try {
      if (isRecurring) {
        // Save to recurring_transactions table
        const recurTemplate = {
          amount: parseFloat(formData.amount),
          category: formData.category,
          source: formData.source,
          type,
          description: formData.description || '',
          attachments: formData.attachmentUrl ? [formData.attachmentUrl] : [],
        };
        const recurPayload = {
          user_id: userId,
          transaction_template: recurTemplate,
          frequency: recurringFrequency as 'daily' | 'weekly' | 'monthly' | 'yearly',
          start_date: formData.date,
          end_date: recurringEndDate || null,
          last_generated: formData.date,
        };
        const { data, error } = await fetch('/api/recurring_transactions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(recurPayload),
        }).then(r => r.json());
        if (error) throw new Error(error.message || 'Failed to create recurring transaction');
        toast({ title: 'Recurring expense scheduled!', description: 'Recurring transaction will be auto-generated.' });
        if (onSubmit) onSubmit(data);
        setLoading(false);
        return;
      }
      // Not recurring: create normal transaction
      const transactionData = {
        user_id: userId,
        amount: parseFloat(formData.amount),
        category: formData.category,
        source: formData.source,
        date: formData.date,
        type,
        description: formData.description || null,
        attachments: formData.attachmentUrl ? [formData.attachmentUrl] : [],
      };
      const result = await addTransaction(transactionData);

      toast({
        title: 'Success',
        description: `${type[0].toUpperCase() + type.slice(1)} added successfully!`
      });

      // Reset form
      setFormData({
        amount: '',
        category: '',
        source: '',
        date: new Date().toISOString().slice(0, 10),
        description: '',
        attachmentUrl: null,
      });

      onSubmit?.(result);
    } catch (err: any) {
      console.error('Error adding transaction:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to add transaction',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6 w-full max-w-md mx-auto bg-white dark:bg-gray-900 p-4 sm:p-6 rounded-2xl shadow-md">
      <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Add {type === 'income' ? 'Income' : 'Expense'}</h3>

      <div>
        <label className="block text-base font-bold mb-1 text-black tracking-wide dark:text-gray-100" htmlFor="amount">Amount</label>
        <div className="relative">
          <input
            id="amount"
            name="amount"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            value={formData.amount}
            onChange={handleChange}
            className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-400 dark:focus:bg-gray-800 dark:placeholder:text-gray-400"
            disabled={loading}
          />
          {errors.amount && <p className="text-red-500 text-sm mt-1 dark:text-red-400">{errors.amount}</p>}
        </div>
      </div>

      <div>
        <label className="block text-base font-bold mb-1 text-black tracking-wide" htmlFor="category">Category</label>
        <select
          id="category"
          name="category"
          value={formData.category}
          onChange={handleChange}
          className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
          disabled={loading || categoriesLoading}
        >
          <option value="">Select category</option>
          {categories.map((cat) => (
            <option key={cat.id} value={cat.name}>{cat.name}</option>
          ))}
          <option value="__custom__">Add new category...</option>
        </select>
        {showCustomCategory && (
          <div className="flex flex-col gap-2 mt-2">
            <input
              type="text"
              value={customCategory}
              onChange={handleCustomCategoryChange}
              className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
              disabled={loading || categoriesLoading}
              placeholder="Enter custom category"
            />
            <button
              type="button"
              className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
              onClick={handleAddCustomCategory}
              disabled={loading || categoriesLoading || !customCategory.trim()}
            >
              Add
            </button>
          </div>
        )}
        {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category}</p>}
      </div>

      <div>
        <label className="block text-base font-bold mb-1 text-black tracking-wide" htmlFor="source">Source</label>
        <input
          id="source"
          name="source"
          type="text"
          placeholder={type === 'income' ? 'e.g., Company Name' : 'e.g., Store Name'}
          value={formData.source}
          onChange={handleChange}
          className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
          disabled={loading}
        />
        {errors.source && <p className="text-red-500 text-sm mt-1">{errors.source}</p>}
      </div>

      <div>
        <label className="block text-base font-bold mb-1 text-black tracking-wide" htmlFor="date">Date</label>
        <input
          id="date"
          name="date"
          type="date"
          value={formData.date}
          onChange={handleChange}
          className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
          disabled={loading}
        />
        {errors.date && <p className="text-red-500 text-sm mt-1">{errors.date}</p>}
      </div>

      {/* Recurring expense feature (only for expenses) */}
      {type === 'expense' && (
        <div className="flex flex-col gap-3 border rounded p-3 bg-gray-50 mt-2">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <input
              type="checkbox"
              checked={isRecurring}
              onChange={e => setIsRecurring(e.target.checked)}
              className="accent-blue-600"
              disabled={loading}
            />
            Is this a recurring expense?
          </label>
          {isRecurring && (
            <div className="flex flex-col gap-2 mt-3">
              <div>
                <label className="block text-xs font-medium mb-1 text-gray-900">Recurrence</label>
                <select
                  value={recurringFrequency}
                  onChange={e => setRecurringFrequency(e.target.value)}
                  className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
                  disabled={loading}
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium mb-1 text-gray-900">End Date (optional)</label>
                <input
                  type="date"
                  value={recurringEndDate}
                  onChange={e => setRecurringEndDate(e.target.value)}
                  className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
                  disabled={loading}
                />
              </div>
            </div>
          )}
        </div>
      )}
      <div>
        <label className="block text-base font-bold mb-1 text-black tracking-wide" htmlFor="description">Description (Optional)</label>
        <textarea
          id="description"
          name="description"
          placeholder="Add any additional details..."
          value={formData.description}
          onChange={handleChange}
          className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
          disabled={loading}
          rows={3}
        />
      </div>

      <div className="mb-4">
        <label
          htmlFor="attachment"
          className="block text-base font-semibold text-gray-800 mb-2"
        >
          Attach file <span className="font-normal text-gray-600">(Optional, max 5MB)</span>
        </label>
        <input
          type="file"
          id="attachment"
          className="w-full text-lg font-semibold text-black bg-white border-2 border-black px-4 py-3 rounded focus:outline-none focus:ring-4 focus:ring-yellow-400 focus:bg-yellow-50 transition-all disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-400 placeholder:text-gray-700"
          onChange={(e) => {
            if (e.target.files && e.target.files[0]) {
              const file = e.target.files[0];
              const reader = new FileReader();
              reader.onload = () => {
                setFormData((prev) => ({ ...prev, attachmentUrl: reader.result as string }));
              };
              reader.readAsDataURL(file);
            }
          }}
          disabled={loading}
        />
      </div>

      {formData.attachmentUrl && (
        <div className="text-sm text-green-600">✓ File attached</div>
      )}
      <div className="flex flex-row gap-3 justify-end mt-4">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
            disabled={loading}
          >
            Cancel
          </button>
        )}
        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={loading}
        >
          {loading ? 'Saving...' : `Add ${type === 'income' ? 'Income' : 'Expense'}`}
        </button>
      </div>
    </form>
  );
}

const Transactions = () => {
  const { user } = useAuth();
  const { currencySymbol } = useCurrency();
  const { paymentMethods } = usePaymentMethod();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showAddModal, setShowAddModal] = useState(false);
  const [addType, setAddType] = useState<null | 'income' | 'expense'>(null);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);
  const [selectMode, setSelectMode] = useState(false);
  const [showBulkExportModal, setShowBulkExportModal] = useState(false);
  const [bulkMode, setBulkMode] = useState(false);
  const [activeTab, setActiveTab] = useState<'transactions' | 'analytics' | 'insights'>('transactions');

  // Use the TransactionHistoryContext
  const {
    filteredTransactions,
    loading,
    error,
    refreshTransactions,
    getTransactionById,
    deleteTransactionItem,
    updateTransactionItem
  } = useTransactionHistory();

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle transaction click to show details or select
  const handleTransactionClick = (id: string) => {
    if (selectMode) {
      // In select mode, toggle selection
      setSelectedTransactions(prev => {
        if (prev.includes(id)) {
          return prev.filter(txId => txId !== id);
        } else {
          return [...prev, id];
        }
      });
    } else {
      // Normal mode, show details
      const transaction = getTransactionById(id);
      if (transaction) {
        setSelectedTransaction(transaction);
        setShowDetailsModal(true);
      }
    }
  };

  // Toggle select mode
  const toggleSelectMode = () => {
    setSelectMode(prev => !prev);
    if (selectMode) {
      // Clear selections when exiting select mode
      setSelectedTransactions([]);
    }
  };

  // Select all transactions
  const selectAllTransactions = () => {
    if (selectedTransactions.length === filteredTransactions.length) {
      // If all are selected, deselect all
      setSelectedTransactions([]);
    } else {
      // Otherwise, select all
      setSelectedTransactions(filteredTransactions.map(tx => tx.id));
    }
  };

  // Delete selected transactions
  const deleteSelectedTransactions = async () => {
    if (window.confirm(`Are you sure you want to delete ${selectedTransactions.length} transaction(s)?`)) {
      try {
        // Delete each selected transaction
        for (const id of selectedTransactions) {
          await deleteTransactionItem(id);
        }
        // Clear selections and refresh
        setSelectedTransactions([]);
        refreshTransactions();
      } catch (error) {
        console.error('Error deleting transactions:', error);
        alert('Failed to delete some transactions. Please try again.');
      }
    }
  };

  // State for edit transaction modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<any>(null);

  // Handle edit transaction
  const handleEditTransaction = () => {
    // Close details modal and open edit form
    setShowDetailsModal(false);
    setEditingTransaction(selectedTransaction);
    setShowEditModal(true);
  };

  // Handle transaction form submission
  const handleTransactionSubmit = () => {
    setShowAddModal(false);
    setAddType(null);
    refreshTransactions();
  };

  // Enhanced bulk operation handlers
  const handleToggleSelection = (id: string) => {
    setSelectedTransactions(prev =>
      prev.includes(id)
        ? prev.filter(txId => txId !== id)
        : [...prev, id]
    );
  };

  const handleToggleSelectAll = () => {
    setSelectedTransactions(prev =>
      prev.length === filteredTransactions.length
        ? []
        : filteredTransactions.map(tx => tx.id)
    );
  };

  const handleBulkDelete = async (ids: string[]) => {
    try {
      await Promise.all(ids.map(id => deleteTransactionItem(id)));
      setSelectedTransactions([]);
      setBulkMode(false);
    } catch (error) {
      console.error('Error deleting transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete some transactions. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleBulkUpdate = async (ids: string[], updates: any) => {
    try {
      await Promise.all(ids.map(id => updateTransactionItem(id, updates)));
      setSelectedTransactions([]);
    } catch (error) {
      console.error('Error updating transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to update some transactions. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleClearSelection = () => {
    setSelectedTransactions([]);
    setBulkMode(false);
  };

  const handleViewTransaction = (id: string) => {
    const transaction = getTransactionById(id);
    if (transaction) {
      setSelectedTransaction(transaction);
      setShowDetailsModal(true);
    }
  };

  const handleEditTransactionById = (id: string) => {
    const transaction = getTransactionById(id);
    if (transaction) {
      setEditingTransaction(transaction);
      setShowEditModal(true);
    }
  };

  const handleDeleteTransactionById = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this transaction?')) {
      try {
        await deleteTransactionItem(id);
        toast({
          title: 'Transaction Deleted',
          description: 'The transaction has been deleted successfully.',
        });
      } catch (error) {
        console.error('Error deleting transaction:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete transaction. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  // State to track highlighted transaction from URL
  const [highlightedTransactionId, setHighlightedTransactionId] = useState<string | null>(null);

  // Handle ESC key to close modals
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showBulkExportModal) {
          setShowBulkExportModal(false);
        } else if (showDetailsModal) {
          setShowDetailsModal(false);
          setSelectedTransaction(null);
        } else if (showAddModal) {
          setShowAddModal(false);
          setAddType(null);
        } else if (showEditModal) {
          setShowEditModal(false);
          setEditingTransaction(null);
        } else if (showFilterModal) {
          setShowFilterModal(false);
        }
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [showBulkExportModal, showDetailsModal, showAddModal, showEditModal, showFilterModal]);

  // Check for transaction ID in URL when component mounts
  useEffect(() => {
    if (user?.id) {
      const searchParams = new URLSearchParams(location.search);
      const transactionId = searchParams.get('id');

      if (transactionId) {
        setHighlightedTransactionId(transactionId);

        // Function to handle transaction found
        const handleTransactionFound = (transaction: any) => {
          setSelectedTransaction(transaction);
          setShowDetailsModal(true);

          // Scroll to the transaction after a short delay to ensure the list is rendered
          setTimeout(() => {
            const element = document.getElementById(`transaction-${transactionId}`);
            if (element) {
              // Add a pulsing animation to make it more visible
              element.classList.add('animate-pulse');
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });

              // Remove the animation after a few seconds
              setTimeout(() => {
                element.classList.remove('animate-pulse');
              }, 2000);
            }
          }, 300);
        };

        const transaction = getTransactionById(transactionId);
        if (transaction) {
          handleTransactionFound(transaction);
        } else {
          // Transaction might not be loaded yet, refresh and try again
          refreshTransactions().then(() => {
            const transactionAfterRefresh = getTransactionById(transactionId);
            if (transactionAfterRefresh) {
              handleTransactionFound(transactionAfterRefresh);
            } else {
              console.error('Transaction not found even after refresh:', transactionId);
              // Try one more time with a longer delay
              setTimeout(() => {
                refreshTransactions().then(() => {
                  const finalAttempt = getTransactionById(transactionId);
                  if (finalAttempt) {
                    handleTransactionFound(finalAttempt);
                  } else {
                    console.error('Transaction not found after multiple attempts:', transactionId);
                  }
                });
              }, 1000);
            }
          });
        }
      }
    }
  }, [user?.id, location.search, getTransactionById, refreshTransactions]);

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header with title and actions */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mt-8 mb-4">
        <div>
          <h1 className="text-2xl font-bold">Transaction History</h1>

          {/* Tab Navigation */}
          <div className="flex gap-1 mt-3">
            <button
              onClick={() => setActiveTab('transactions')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'transactions'
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
              }`}
            >
              Transactions
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'analytics'
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
              }`}
            >
              Analytics
            </button>
            <button
              onClick={() => setActiveTab('insights')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'insights'
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
              }`}
            >
              Insights
            </button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {/* Bulk Mode Toggle */}
          {activeTab === 'transactions' && (
            <button
              onClick={() => setBulkMode(!bulkMode)}
              className={`px-3 py-1.5 rounded-lg text-sm flex items-center gap-1 ${
                bulkMode
                  ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              {bulkMode ? 'Exit Bulk Mode' : 'Bulk Mode'}
            </button>
          )}

          {/* Import/Export Buttons */}
          <div className="flex gap-2">
            <button
              onClick={() => navigate('/transactions/import')}
              className="px-3 py-1.5 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-1"
            >
              <FileUp className="h-4 w-4" />
              <span>Import</span>
            </button>

            {filteredTransactions.length > 0 && (
              <button
                onClick={() => setShowBulkExportModal(true)}
                className="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                <span>Export</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'transactions' && (
        <>
          {/* Quick Filters */}
          <TransactionQuickFilters />

          {/* Search and Filter Bar */}
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <TransactionSearch />

            <div className="flex gap-2 ml-auto">
              <button
                className="px-3 py-1.5 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 flex items-center gap-1 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-800/50"
                onClick={() => setShowFilterModal(true)}
              >
                <Filter className="h-4 w-4" />
                <span>Advanced Filters</span>
              </button>

              <TransactionSorting />
            </div>
          </div>

          {/* Bulk Operations */}
          {bulkMode && (
            <TransactionBulkOperations
              selectedTransactions={selectedTransactions}
              onClearSelection={handleClearSelection}
              onBulkDelete={handleBulkDelete}
              onBulkUpdate={handleBulkUpdate}
            />
          )}

          {/* Enhanced Transaction Table */}
          <EnhancedTransactionTable
            onViewTransaction={handleViewTransaction}
            onEditTransaction={handleEditTransactionById}
            onDeleteTransaction={handleDeleteTransactionById}
            selectedTransactions={selectedTransactions}
            onToggleSelection={handleToggleSelection}
            onToggleSelectAll={handleToggleSelectAll}
            bulkMode={bulkMode}
          />

          {/* Pagination */}
          <TransactionPagination />
        </>
      )}

      {activeTab === 'analytics' && (
        <TransactionAnalytics />
      )}

      {activeTab === 'insights' && (
        <TransactionInsights />
      )}

      {/* Add Transaction Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-900/80 dark:backdrop-blur-xl rounded-2xl shadow-2xl p-6 relative border border-gray-200 dark:border-gray-700 w-full max-w-lg">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
              onClick={() => setShowAddModal(false)}
            >
              <X className="h-5 w-5" />
            </button>
            <EnhancedTransactionForm
              userId={user?.id || ''}
              type={addType || 'expense'}
              onSubmit={handleTransactionSubmit}
              onCancel={() => setShowAddModal(false)}
            />
          </div>
        </div>
      )}

      {/* Filter Modal */}
      {showFilterModal && (
        <FilterModal
          onClose={() => setShowFilterModal(false)}
          onApply={() => setShowFilterModal(false)}
        />
      )}

      {/* Transaction Details Modal */}
      {showDetailsModal && selectedTransaction && (
        <TransactionDetailsModal
          transaction={selectedTransaction}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedTransaction(null);
          }}
          onEdit={handleEditTransaction}
        />
      )}

      {/* Edit Transaction Modal */}
      {showEditModal && editingTransaction && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-900/80 dark:backdrop-blur-xl rounded-2xl shadow-2xl p-6 relative border border-gray-200 dark:border-gray-700 w-full max-w-lg">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
              onClick={() => {
                setShowEditModal(false);
                setEditingTransaction(null);
              }}
            >
              <X className="h-5 w-5" />
            </button>
            <EnhancedTransactionForm
              userId={user?.id || ''}
              type={editingTransaction.type}
              existingTransaction={editingTransaction}
              isEditing={true}
              onSubmit={() => {
                setShowEditModal(false);
                setEditingTransaction(null);
                refreshTransactions();
              }}
              onCancel={() => {
                setShowEditModal(false);
                setEditingTransaction(null);
              }}
            />
          </div>
        </div>
      )}

      {/* Bulk Export Modal */}
      {showBulkExportModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-900/80 dark:backdrop-blur-xl rounded-2xl shadow-2xl relative border border-gray-200 dark:border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <button
              className="absolute top-4 right-4 z-10 text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
              onClick={() => setShowBulkExportModal(false)}
            >
              <X className="h-5 w-5" />
            </button>
            <div className="p-6 overflow-y-auto max-h-[90vh]">
              <BulkExport />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Transactions;