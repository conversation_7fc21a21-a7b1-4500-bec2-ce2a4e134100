import React, { useState, useRef, useEffect } from 'react';
import { useCurrency } from '../../contexts/CurrencyContext';

interface AmountInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

export function AmountInput({ value, onChange, error }: AmountInputProps) {
  const { currencySymbol, formatCurrency } = useCurrency();
  const inputRef = useRef<HTMLInputElement>(null);
  const [cursorPosition, setCursorPosition] = useState<number | null>(null);

  // Handle cursor position after value changes
  useEffect(() => {
    if (cursorPosition !== null && inputRef.current) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
    }
  }, [value, cursorPosition]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    const oldValue = value;
    const oldSelectionStart = input.selectionStart || 0;

    // Remove all non-numeric characters except decimal point
    let rawValue = input.value.replace(/[^0-9.]/g, '');

    // Ensure only one decimal point
    const parts = rawValue.split('.');
    const formattedValue = parts.length > 1
      ? `${parts[0]}.${parts.slice(1).join('')}`
      : rawValue;

    // Calculate new cursor position
    let newCursorPosition = oldSelectionStart;

    // Adjust cursor position if characters were removed
    if (formattedValue.length < input.value.length) {
      const diff = input.value.length - formattedValue.length;
      newCursorPosition = Math.max(0, oldSelectionStart - diff);
    }

    // Limit to 2 decimal places if decimal exists
    if (formattedValue.includes('.')) {
      const [whole, decimal] = formattedValue.split('.');
      if (decimal.length > 2) {
        const truncatedValue = `${whole}.${decimal.substring(0, 2)}`;

        // Only adjust cursor if it's after the decimal point
        if (oldSelectionStart > whole.length + 1) {
          const decimalPosition = oldSelectionStart - (whole.length + 1);
          if (decimalPosition > 2) {
            newCursorPosition = whole.length + 3; // Position after 2nd decimal place
          }
        }

        // Update the value
        onChange(truncatedValue);
        setCursorPosition(newCursorPosition);
        return;
      }
    }

    // Update the value and cursor position
    onChange(formattedValue);
    setCursorPosition(newCursorPosition);
  };

  return (
    <div className="space-y-1">
      <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Amount
      </label>
      <div className="relative rounded-md shadow-sm">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span className="text-gray-500 sm:text-sm">{currencySymbol}</span>
        </div>
        <input
          ref={inputRef}
          type="text"
          id="amount"
          className={`block w-full pl-7 pr-12 py-2 sm:text-sm rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            error ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'
          }`}
          placeholder="0.00"
          value={value}
          onChange={handleChange}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? 'amount-error' : undefined}
        />
        {value && !isNaN(parseFloat(value)) && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-sm text-gray-500">
              {formatCurrency(parseFloat(value)).replace(currencySymbol, '')}
            </span>
          </div>
        )}
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600" id="amount-error">
          {error}
        </p>
      )}
    </div>
  );
}
