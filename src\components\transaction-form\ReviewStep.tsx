import React, { useState, useEffect } from 'react';
import { addTransaction, getTransactions } from '../../lib/transactions';
import { addRecurringTransaction } from '../../lib/recurringTransactions';
import { useToast } from '../../components/ui/use-toast';
import { useCurrency } from '../../contexts/CurrencyContext';
import { usePaymentMethod } from '../../contexts/PaymentMethodContext';
import { useTransactionHistory } from '../../contexts/TransactionHistoryContext';
import { detectDuplicate } from '../../lib/smartCategories';
import { DuplicateWarning } from './DuplicateWarning';
import { ChevronLeft, Check, Loader2 } from 'lucide-react';

interface ReviewStepProps {
  formData: any;
  type: 'income' | 'expense';
  userId: string;
  onSubmit: (data: any) => void;
  onPrev: () => void;
  existingTransaction?: any; // For editing existing transactions
  isEditing?: boolean;
}

export function ReviewStep({
  formData,
  type,
  userId,
  onSubmit,
  onPrev,
  existingTransaction,
  isEditing = false
}: ReviewStepProps) {
  const [loading, setLoading] = useState(false);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [duplicateWarning, setDuplicateWarning] = useState<any>(null);
  const { toast } = useToast();
  const { currencySymbol } = useCurrency();
  const { refreshPaymentMethods } = usePaymentMethod();
  const { updateTransactionItem } = useTransactionHistory();

  // Load recent transactions to check for duplicates
  useEffect(() => {
    const loadRecentTransactions = async () => {
      try {
        // Get transactions from the last 7 days
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const transactions = await getTransactions({
          userId,
          startDate: sevenDaysAgo.toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
        });

        setRecentTransactions(transactions);
      } catch (error) {
        console.error('Error loading recent transactions:', error);
      }
    };

    loadRecentTransactions();
  }, [userId]);

  // Check for duplicate transactions
  useEffect(() => {
    if (recentTransactions.length > 0 && formData.amount && formData.category) {
      const newTransaction = {
        amount: parseFloat(formData.amount),
        category: formData.category,
        source: formData.source,
        date: formData.date,
        type,
      };

      const { isDuplicate, similarTransaction } = detectDuplicate(
        newTransaction,
        recentTransactions
      );

      if (isDuplicate) {
        setDuplicateWarning(similarTransaction);
      } else {
        setDuplicateWarning(null);
      }
    }
  }, [recentTransactions, formData, type]);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      // Prepare transaction data
      const transactionData = {
        user_id: userId,
        amount: parseFloat(formData.amount),
        type,
        category: formData.category,
        source: formData.source,
        date: `${formData.date}T${formData.time || '00:00'}`,
        description: formData.description || null,
        // Ensure attachments is always an array
        attachments: Array.isArray(formData.attachments) ? formData.attachments : [],
        metadata: {
          paymentMethod: type === 'expense' ? formData.paymentMethod : null,
          creditCardId: formData.creditCardId || null, // Include credit card ID in metadata
          subcategory: formData.subcategory || null,
          // Ensure tags is always an array
          tags: Array.isArray(formData.tags) && formData.tags.length > 0 ? formData.tags : [],
          isTaxDeductible: formData.isTaxDeductible || false,
          taxCategory: formData.isTaxDeductible ? formData.taxCategory : null,
          location: formData.location || null,
          splitItems: formData.isSplit && Array.isArray(formData.splitItems) ? formData.splitItems : [],
        },
      };

      if (formData.isRecurring) {
        // Handle recurring transaction
        const recurTemplate = {
          amount: parseFloat(formData.amount),
          category: formData.category,
          source: formData.source,
          type,
          description: formData.description || '',
          // Ensure attachments is always an array
          attachments: Array.isArray(formData.attachments) ? formData.attachments : [],
          metadata: transactionData.metadata,
        };

        const recurPayload = {
          user_id: userId,
          transaction_template: recurTemplate,
          frequency: formData.recurringFrequency,
          start_date: formData.date,
          end_date: formData.recurringEndDate || null,
          last_generated: formData.date,
        };

        try {
          // Use our client-side function instead of API call
          const result = await addRecurringTransaction(recurPayload);

          toast({
            title: 'Recurring transaction scheduled!',
            description: 'Recurring transaction will be auto-generated.'
          });

          onSubmit(result);
        } catch (error: any) {
          console.error('Error creating recurring transaction:', error);
          toast({
            title: 'Error',
            description: error.message || 'Failed to create recurring transaction. Please try again.',
            variant: 'destructive'
          });
        }
      } else {
        let result;

        // Check if we're editing an existing transaction or creating a new one
        if (isEditing && existingTransaction?.id) {
          // Update existing transaction
          await updateTransactionItem(existingTransaction.id, transactionData);
          result = { ...existingTransaction, ...transactionData };

          toast({
            title: 'Success',
            description: `${type[0].toUpperCase() + type.slice(1)} updated successfully!`
          });
        } else {
          // Create new transaction
          result = await addTransaction(transactionData);

          // If this is a split transaction, create the split transactions
          if (formData.isSplit && formData.splitItems && formData.splitItems.length > 0) {
            // In a real implementation, you would handle split transactions here
            // This could involve creating multiple transactions or storing the split info
          }

          toast({
            title: 'Success',
            description: `${type[0].toUpperCase() + type.slice(1)} added successfully!`
          });
        }

        // If this is a credit card transaction, refresh payment methods to update balances
        if (type === 'expense' && formData.paymentMethod) {
          try {
            await refreshPaymentMethods();
          } catch (refreshErr) {
            console.error('Error refreshing payment methods:', refreshErr);
            // Continue even if refresh fails
          }
        }

        onSubmit(result);
      }
    } catch (err: any) {
      console.error('Error adding transaction:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to add transaction',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string, timeString?: string) => {
    const date = new Date(`${dateString}T${timeString || '00:00'}`);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: timeString ? '2-digit' : undefined,
      minute: timeString ? '2-digit' : undefined,
    });
  };

  // Always render the DuplicateWarning component, but conditionally show it
  // This ensures hooks are always called in the same order
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
        Review Transaction
      </h3>

      {/* Always render DuplicateWarning but conditionally show it */}
      <div style={{ display: duplicateWarning ? 'block' : 'none' }}>
        <DuplicateWarning
          similarTransaction={duplicateWarning || {
            id: '',
            amount: 0,
            category: '',
            source: '',
            date: '',
            type: type
          }}
          onContinue={() => setDuplicateWarning(null)}
          onCancel={onPrev}
        />
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
        <div className="flex justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">Type</span>
          <span className={`text-sm font-medium ${
            type === 'income' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
          }`}>
            {type === 'income' ? 'Income' : 'Expense'}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">Amount</span>
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {currencySymbol}{parseFloat(formData.amount).toFixed(2)}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">Category</span>
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {formData.category}
            {formData.subcategory && ` / ${formData.subcategory}`}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {type === 'income' ? 'Source' : 'Merchant/Vendor'}
          </span>
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {formData.source}
          </span>
        </div>

        {type === 'expense' && formData.paymentMethod && (
          <div className="flex justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">Payment Method</span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formData.paymentMethod}
              {formData.creditCardId && (
                <span className="ml-1 text-xs text-blue-600 dark:text-blue-400">
                  (Card ID: {formData.creditCardId.substring(0, 8)}...)
                </span>
              )}
            </span>
          </div>
        )}

        <div className="flex justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">Date & Time</span>
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {formatDate(formData.date, formData.time)}
          </span>
        </div>

        {formData.description && (
          <div className="flex justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">Description</span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100 max-w-[60%] text-right">
              {formData.description}
            </span>
          </div>
        )}

        {formData.location && (
          <div className="flex justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">Location</span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100 max-w-[60%] text-right">
              {formData.location}
            </span>
          </div>
        )}

        {formData.tags && formData.tags.length > 0 && (
          <div className="flex justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">Tags</span>
            <div className="flex flex-wrap justify-end gap-1 max-w-[60%]">
              {formData.tags.map((tag: string, index: number) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {formData.isTaxDeductible && (
          <div className="flex justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">Tax Info</span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Tax Deductible {formData.taxCategory ? `(${formData.taxCategory.replace('_', ' ')})` : ''}
            </span>
          </div>
        )}

        {formData.isRecurring && (
          <div className="flex justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">Recurring</span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formData.recurringFrequency.charAt(0).toUpperCase() + formData.recurringFrequency.slice(1)}
              {formData.recurringEndDate ? ` until ${new Date(formData.recurringEndDate).toLocaleDateString()}` : ' (no end date)'}
            </span>
          </div>
        )}

        {formData.isSplit && formData.splitItems && formData.splitItems.length > 0 && (
          <div className="pt-2">
            <span className="text-sm text-gray-500 dark:text-gray-400 block mb-1">Split Items:</span>
            <div className="space-y-1 pl-2">
              {formData.splitItems.map((item: any, index: number) => (
                <div key={index} className="flex justify-between text-xs">
                  <span className="text-gray-700 dark:text-gray-300">{item.category || 'Uncategorized'}</span>
                  <span className="font-medium">{currencySymbol}{parseFloat(item.amount).toFixed(2)}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={onPrev}
          className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 flex items-center"
          disabled={loading}
        >
          <ChevronLeft className="mr-1 h-4 w-4" />
          Back
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Check className="mr-1 h-4 w-4" />
              {isEditing ? 'Update Transaction' : 'Save Transaction'}
            </>
          )}
        </button>
      </div>
    </div>
  );
}
