#!/usr/bin/env node
require('dotenv').config();
const https = require('https');

const BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

if (!BOT_TOKEN) {
  console.error('❌ TELEGRAM_BOT_TOKEN not found in .env file');
  process.exit(1);
}

console.log('🧹 Clearing Telegram webhook...');

// Clear webhook
const clearWebhook = () => {
  const options = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/deleteWebhook`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const req = https.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.ok) {
          console.log('✅ Webhook cleared successfully');
          console.log('📝 Response:', response.description || 'Webhook deleted');
        } else {
          console.log('⚠️ Response:', response.description);
        }
      } catch (error) {
        console.error('❌ Error parsing response:', error);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Error clearing webhook:', error);
  });

  req.end();
};

// Get current webhook info first
const getWebhookInfo = () => {
  const options = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/getWebhookInfo`,
    method: 'GET'
  };

  const req = https.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.ok) {
          const info = response.result;
          console.log('📡 Current webhook info:');
          console.log('   URL:', info.url || 'None');
          console.log('   Pending updates:', info.pending_update_count || 0);
          console.log('   Last error:', info.last_error_message || 'None');
          
          if (info.url) {
            console.log('🧹 Clearing existing webhook...');
            clearWebhook();
          } else {
            console.log('✅ No webhook set - ready for polling');
          }
        }
      } catch (error) {
        console.error('❌ Error parsing webhook info:', error);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Error getting webhook info:', error);
  });

  req.end();
};

// Start the process
getWebhookInfo();
