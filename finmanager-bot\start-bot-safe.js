#!/usr/bin/env node
require('dotenv').config();
const { spawn } = require('child_process');
const https = require('https');

const BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

console.log('🚀 Safe Bot Startup - Checking for conflicts...');

// Check if webhook is set
const checkWebhook = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.telegram.org',
      port: 443,
      path: `/bot${BOT_TOKEN}/getWebhookInfo`,
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response.result);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
};

// Clear webhook if set
const clearWebhook = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.telegram.org',
      port: 443,
      path: `/bot${BOT_TOKEN}/deleteWebhook`,
      method: 'POST'
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
};

// Test bot connection
const testBot = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.telegram.org',
      port: 443,
      path: `/bot${BOT_TOKEN}/getMe`,
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.ok) {
            console.log('✅ Bot token is valid');
            console.log(`🤖 Bot name: ${response.result.first_name}`);
            console.log(`📝 Username: @${response.result.username}`);
            resolve(response.result);
          } else {
            reject(new Error(`Bot test failed: ${response.description}`));
          }
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
};

// Main startup process
const startBot = async () => {
  try {
    console.log('🔍 Step 1: Testing bot token...');
    await testBot();

    console.log('🔍 Step 2: Checking webhook status...');
    const webhookInfo = await checkWebhook();
    
    if (webhookInfo.url) {
      console.log(`⚠️ Webhook is set: ${webhookInfo.url}`);
      console.log('🧹 Clearing webhook for polling mode...');
      await clearWebhook();
      console.log('✅ Webhook cleared');
      
      // Wait a moment for Telegram to process
      console.log('⏳ Waiting 3 seconds for Telegram to process...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    } else {
      console.log('✅ No webhook set - ready for polling');
    }

    console.log('🔍 Step 3: Starting bot with enhanced error handling...');
    
    // Start the main bot
    const botProcess = spawn('node', ['finmanager-bot.js'], {
      stdio: 'inherit',
      cwd: __dirname
    });

    botProcess.on('error', (error) => {
      console.error('❌ Failed to start bot:', error);
    });

    botProcess.on('exit', (code) => {
      console.log(`🛑 Bot process exited with code ${code}`);
      if (code !== 0) {
        console.log('🔄 Restarting in 5 seconds...');
        setTimeout(() => {
          startBot();
        }, 5000);
      }
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down bot...');
      botProcess.kill('SIGINT');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Startup error:', error.message);
    console.log('🔄 Retrying in 10 seconds...');
    setTimeout(startBot, 10000);
  }
};

// Validate environment
if (!BOT_TOKEN) {
  console.error('❌ TELEGRAM_BOT_TOKEN not found in .env file');
  process.exit(1);
}

if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Supabase credentials not found in .env file');
  process.exit(1);
}

// Start the bot
startBot();
