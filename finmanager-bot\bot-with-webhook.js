#!/usr/bin/env node
require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');
const express = require('express');
const FiNManageRTelegramBot = require('./finmanager-bot.js');

/**
 * Alternative Bot Startup with Webhook Support
 * Use this if polling has network issues
 */

const PORT = process.env.PORT || 3000;
const WEBHOOK_URL = process.env.WEBHOOK_URL; // e.g., https://yourdomain.com/webhook

class WebhookBotRunner {
  constructor() {
    this.app = express();
    this.app.use(express.json());
    
    console.log('🌐 Starting FiNManageR Bot with Webhook Support');
    
    if (WEBHOOK_URL) {
      this.startWithWebhook();
    } else {
      this.startWithPolling();
    }
  }

  startWithWebhook() {
    console.log('🔗 Starting bot with webhook mode...');
    
    // Create bot without polling
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: false });
    
    // Set webhook
    bot.setWebHook(`${WEBHOOK_URL}/webhook`).then(() => {
      console.log('✅ Webhook set successfully');
    }).catch(error => {
      console.error('❌ Failed to set webhook:', error);
      this.fallbackToPolling();
    });

    // Webhook endpoint
    this.app.post('/webhook', (req, res) => {
      try {
        bot.processUpdate(req.body);
        res.sendStatus(200);
      } catch (error) {
        console.error('Webhook processing error:', error);
        res.sendStatus(500);
      }
    });

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        mode: 'webhook',
        timestamp: new Date().toISOString()
      });
    });

    // Initialize the main bot class
    this.finBot = new FiNManageRTelegramBot(process.env.TELEGRAM_BOT_TOKEN);
    
    // Start server
    this.app.listen(PORT, () => {
      console.log(`🚀 Webhook server running on port ${PORT}`);
      console.log(`📡 Webhook URL: ${WEBHOOK_URL}/webhook`);
    });
  }

  startWithPolling() {
    console.log('🔄 Starting bot with enhanced polling...');
    
    try {
      this.finBot = new FiNManageRTelegramBot(process.env.TELEGRAM_BOT_TOKEN);
      console.log('✅ Bot started with polling mode');
    } catch (error) {
      console.error('❌ Failed to start bot with polling:', error);
      process.exit(1);
    }
  }

  fallbackToPolling() {
    console.log('🔄 Falling back to polling mode...');
    setTimeout(() => {
      this.startWithPolling();
    }, 5000);
  }
}

// Health check endpoint for polling mode
if (!WEBHOOK_URL) {
  const app = express();
  app.get('/health', (req, res) => {
    res.json({ 
      status: 'healthy', 
      mode: 'polling',
      timestamp: new Date().toISOString()
    });
  });
  
  app.listen(PORT, () => {
    console.log(`🏥 Health check server running on port ${PORT}`);
  });
}

// Start the bot
if (require.main === module) {
  console.log('🎉 Starting FiNManageR Telegram Bot with Network Resilience');
  
  // Validate environment
  if (!process.env.TELEGRAM_BOT_TOKEN) {
    console.error('❌ TELEGRAM_BOT_TOKEN not found');
    process.exit(1);
  }
  
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Supabase credentials not found');
    process.exit(1);
  }
  
  new WebhookBotRunner();
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down bot...');
    process.exit(0);
  });
}

module.exports = WebhookBotRunner;
