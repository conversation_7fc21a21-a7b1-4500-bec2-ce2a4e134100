import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';
import { useToast } from '../components/ui/use-toast';
import { v4 as uuidv4 } from 'uuid';

export interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  color?: string;
  icon?: string;
  isDefault: boolean;
  userId: string;
}

interface CategoryContextType {
  categories: Category[];
  expenseCategories: Category[];
  incomeCategories: Category[];
  addCategory: (category: Omit<Category, 'id' | 'userId'>) => Promise<Category>;
  updateCategory: (id: string, updates: Partial<Omit<Category, 'id' | 'userId' | 'isDefault'>>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  checkCategoryExists: (name: string, type: 'income' | 'expense') => boolean;
  loading: boolean;
  error: string | null;
  refreshCategories: () => Promise<void>;
}

// Default categories
const DEFAULT_EXPENSE_CATEGORIES = [
  'Food & Dining', 'Groceries', 'Transportation', 'Shopping', 'Entertainment',
  'Bills & Utilities', 'Mobile Recharge', 'DTH/Cable', 'Internet', 'Electricity',
  'Rent', 'Healthcare', 'Education', 'Travel', 'Household', 'Personal Care',
  'Domestic Help', 'LPG/Gas', 'Insurance', 'Investments', 'EMI/Loan', 'Other'
];

const DEFAULT_INCOME_CATEGORIES = [
  'Salary', 'Freelance', 'Business', 'Investment', 'Rental', 'Interest', 
  'Dividend', 'Gift', 'Refund', 'Other'
];

const CategoryContext = createContext<CategoryContextType | undefined>(undefined);

export const CategoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [addingCategories, setAddingCategories] = useState<Set<string>>(new Set());

  // Filter categories by type
  const expenseCategories = categories.filter(cat => cat.type === 'expense');
  const incomeCategories = categories.filter(cat => cat.type === 'income');

  // Migrate from localStorage to Supabase
  const migrateFromLocalStorage = async () => {
    if (!user) return;

    try {
      // Get custom categories from localStorage
      const migrateCategory = async (type: 'income' | 'expense') => {
        const key = `custom_categories_${user.id}_${type}`;
        const stored = localStorage.getItem(key);
        
        if (stored) {
          const customCategories: string[] = JSON.parse(stored);
          
          // Check if these categories already exist in Supabase
          const { data: existingCategories } = await supabase
            .from('categories')
            .select('name')
            .eq('user_id', user.id)
            .eq('type', type);
          
          const existingNames = existingCategories?.map(cat => cat.name) || [];
          
          // Add categories that don't exist yet
          for (const categoryName of customCategories) {
            if (!existingNames.includes(categoryName)) {
              await supabase.from('categories').insert({
                id: uuidv4(),
                name: categoryName,
                type,
                is_default: false,
                user_id: user.id
              });
            }
          }
        }
      };
      
      await migrateCategory('income');
      await migrateCategory('expense');
      
      // After migration, we can clear localStorage (optional)
      // localStorage.removeItem(`custom_categories_${user.id}_income`);
      // localStorage.removeItem(`custom_categories_${user.id}_expense`);
    } catch (err) {
      console.error('Error migrating categories from localStorage:', err);
    }
  };

  // Initialize default categories for new users
  const initializeDefaultCategories = async () => {
    if (!user) return;

    try {
      // Check if user already has categories
      const { data: existingCategories, error: checkError } = await supabase
        .from('categories')
        .select('id')
        .eq('user_id', user.id);

      if (checkError) throw checkError;

      // If user has no categories, add defaults
      if (!existingCategories || existingCategories.length === 0) {
        const defaultCategories = [
          ...DEFAULT_EXPENSE_CATEGORIES.map(name => ({
            id: uuidv4(),
            name,
            type: 'expense',
            is_default: true,
            user_id: user.id
          })),
          ...DEFAULT_INCOME_CATEGORIES.map(name => ({
            id: uuidv4(),
            name,
            type: 'income',
            is_default: true,
            user_id: user.id
          }))
        ];

        const { error: insertError } = await supabase
          .from('categories')
          .insert(defaultCategories);

        if (insertError) throw insertError;
      }
    } catch (err: any) {
      console.error('Error initializing default categories:', err.message);
      setError('Failed to initialize default categories');
    }
  };

  // Fetch all categories
  const fetchCategories = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error: fetchError } = await supabase
        .from('categories')
        .select('*')
        .eq('user_id', user.id)
        .order('name');

      if (fetchError) throw fetchError;

      setCategories(data || []);
    } catch (err: any) {
      console.error('Error fetching categories:', err.message);
      setError('Failed to load categories');
      toast({
        title: 'Error',
        description: 'Failed to load categories',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh categories
  const refreshCategories = async () => {
    await fetchCategories();
  };

  // Helper function to check if a category already exists
  const checkCategoryExists = (name: string, type: 'income' | 'expense'): boolean => {
    const normalizedName = name.trim();
    if (!normalizedName) return false;

    return categories.some(
      cat => cat.name.toLowerCase() === normalizedName.toLowerCase() &&
             cat.type === type
    );
  };

  // Add a new category with comprehensive duplicate checking and race condition prevention
  const addCategory = async (category: Omit<Category, 'id' | 'userId'>): Promise<Category> => {
    if (!user) throw new Error('User not authenticated');

    // Normalize the category name (trim whitespace and convert to lowercase for comparison)
    const normalizedName = category.name.trim();

    if (!normalizedName) {
      throw new Error('Category name cannot be empty');
    }

    // Create a unique key for this category addition attempt
    const categoryKey = `${user.id}_${category.type}_${normalizedName.toLowerCase()}`;

    // Check if we're already adding this exact category (race condition prevention)
    if (addingCategories.has(categoryKey)) {
      const error = new Error(`Category "${normalizedName}" is already being added. Please wait.`);
      toast({
        title: 'Category In Progress',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    // Mark this category as being added
    setAddingCategories(prev => new Set(prev).add(categoryKey));

    // Check for duplicates in current state first (faster check)
    const existsInState = categories.some(
      cat => cat.name.toLowerCase() === normalizedName.toLowerCase() &&
             cat.type === category.type
    );

    if (existsInState) {
      const error = new Error(`A category named "${normalizedName}" already exists for ${category.type} transactions`);
      toast({
        title: 'Duplicate Category',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    // Double-check in database to handle race conditions
    try {
      const { data: existingCategories, error: checkError } = await supabase
        .from('categories')
        .select('name, type')
        .eq('user_id', user.id)
        .eq('type', category.type)
        .ilike('name', normalizedName); // Case-insensitive check

      if (checkError) {
        console.error('Error checking for duplicate categories:', checkError);
        // Continue with insertion attempt - let database constraint handle it
      } else if (existingCategories && existingCategories.length > 0) {
        const error = new Error(`A category named "${normalizedName}" already exists for ${category.type} transactions`);
        toast({
          title: 'Duplicate Category',
          description: error.message,
          variant: 'destructive',
        });
        throw error;
      }
    } catch (err: any) {
      // If it's our duplicate error, re-throw it
      if (err.message.includes('already exists')) {
        throw err;
      }
      // Otherwise, log the check error but continue with insertion
      console.warn('Database duplicate check failed, proceeding with insertion:', err.message);
    }

    const newCategory = {
      id: uuidv4(),
      ...category,
      name: normalizedName, // Use normalized name
      userId: user.id
    };

    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          id: newCategory.id,
          name: newCategory.name,
          type: newCategory.type,
          color: newCategory.color,
          icon: newCategory.icon,
          is_default: newCategory.isDefault,
          user_id: newCategory.userId
        })
        .select()
        .single();

      if (error) {
        // Handle specific constraint violation errors
        if (error.code === '23505' && error.message.includes('categories_user_id_name_type_key')) {
          const duplicateError = new Error(`A category named "${normalizedName}" already exists for ${category.type} transactions`);
          toast({
            title: 'Duplicate Category',
            description: duplicateError.message,
            variant: 'destructive',
          });
          throw duplicateError;
        }
        throw error;
      }

      setCategories(prev => [...prev, newCategory]);

      toast({
        title: 'Success',
        description: `Category "${newCategory.name}" added successfully`,
      });

      return newCategory;
    } catch (err: any) {
      console.error('Error adding category:', err.message);

      // Don't show generic error if we already showed a specific one
      if (!err.message.includes('already exists') && !err.message.includes('already being added')) {
        toast({
          title: 'Error',
          description: err.message || 'Failed to add category',
          variant: 'destructive',
        });
      }
      throw err;
    } finally {
      // Always clean up the lock, regardless of success or failure
      setAddingCategories(prev => {
        const newSet = new Set(prev);
        newSet.delete(categoryKey);
        return newSet;
      });
    }
  };

  // Update a category
  const updateCategory = async (
    id: string, 
    updates: Partial<Omit<Category, 'id' | 'userId' | 'isDefault'>>
  ): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const { error } = await supabase
        .from('categories')
        .update({
          name: updates.name,
          color: updates.color,
          icon: updates.icon,
        })
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      setCategories(prev => 
        prev.map(cat => 
          cat.id === id ? { ...cat, ...updates } : cat
        )
      );

      toast({
        title: 'Success',
        description: 'Category updated successfully',
      });
    } catch (err: any) {
      console.error('Error updating category:', err.message);
      toast({
        title: 'Error',
        description: 'Failed to update category',
        variant: 'destructive',
      });
      throw err;
    }
  };

  // Delete a category
  const deleteCategory = async (id: string): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      // Check if it's a default category
      const categoryToDelete = categories.find(cat => cat.id === id);
      if (categoryToDelete?.isDefault) {
        throw new Error('Cannot delete default categories');
      }

      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      setCategories(prev => prev.filter(cat => cat.id !== id));

      toast({
        title: 'Success',
        description: 'Category deleted successfully',
      });
    } catch (err: any) {
      console.error('Error deleting category:', err.message);
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete category',
        variant: 'destructive',
      });
      throw err;
    }
  };

  // Initialize categories when user changes
  useEffect(() => {
    if (user) {
      (async () => {
        await initializeDefaultCategories();
        await migrateFromLocalStorage();
        await fetchCategories();
      })();
    } else {
      setCategories([]);
      setLoading(false);
    }
  }, [user?.id]);

  return (
    <CategoryContext.Provider
      value={{
        categories,
        expenseCategories,
        incomeCategories,
        addCategory,
        updateCategory,
        deleteCategory,
        checkCategoryExists,
        loading,
        error,
        refreshCategories
      }}
    >
      {children}
    </CategoryContext.Provider>
  );
};

export const useCategories = () => {
  const context = useContext(CategoryContext);
  if (context === undefined) {
    throw new Error('useCategories must be used within a CategoryProvider');
  }
  return context;
};
