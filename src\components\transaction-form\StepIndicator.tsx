import React from 'react';
import { cn } from '../../lib/utils';

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

export function StepIndicator({ currentStep, totalSteps }: StepIndicatorProps) {
  return (
    <div className="flex items-center justify-between mb-6">
      {Array.from({ length: totalSteps }).map((_, index) => (
        <React.Fragment key={index}>
          {/* Step circle */}
          <div className="flex flex-col items-center">
            <div 
              className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                index + 1 === currentStep 
                  ? "bg-blue-600 text-white" 
                  : index + 1 < currentStep 
                    ? "bg-green-500 text-white" 
                    : "bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-400"
              )}
            >
              {index + 1 < currentStep ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              ) : (
                index + 1
              )}
            </div>
            <span className="text-xs mt-1 text-gray-500 dark:text-gray-400">
              {index === 0 ? 'Basic' : index === 1 ? 'Details' : index === 2 ? 'Options' : 'Review'}
            </span>
          </div>
          
          {/* Connector line between steps */}
          {index < totalSteps - 1 && (
            <div 
              className={cn(
                "h-0.5 flex-1 mx-2",
                index + 1 < currentStep 
                  ? "bg-green-500" 
                  : "bg-gray-200 dark:bg-gray-700"
              )}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
}
